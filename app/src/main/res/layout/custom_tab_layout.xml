<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:gravity="center"
    android:paddingStart="12dp"
    android:paddingEnd="12dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:minWidth="48dp">

    <ImageView
        android:id="@+id/tab_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="6dp"
        android:scaleType="centerInside" />

    <TextView
        android:id="@+id/tab_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/EatTab"
        android:textColor="@color/colorDark50"
        android:visibility="gone"
        android:maxLines="1"
        android:ellipsize="end" />

</LinearLayout>
