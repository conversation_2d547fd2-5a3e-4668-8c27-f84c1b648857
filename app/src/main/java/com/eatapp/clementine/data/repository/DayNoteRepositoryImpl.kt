package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.response.daynote.DayNoteResponse
import com.eatapp.clementine.data.network.response.daynote.DayNotesResponse
import com.eatapp.clementine.internal.simpleDate
import java.util.Date
import javax.inject.Inject

class DayNoteRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : DayNoteRepository {

    override suspend fun dayNote(date: Date): DayNotesResponse
            = eatApiRestaurant.dayNoteAsync(simpleDate(date))

    override suspend fun insertDayNote(date: Date, content: String): DayNoteResponse
            = eatApiRestaurant.insertDayNoteAsync(simpleDate(date), content)

    override suspend fun updateDayNote(noteId: String, content: String): DayNoteResponse
            = eatApiRestaurant.updateDayNoteAsync(noteId, content)
}