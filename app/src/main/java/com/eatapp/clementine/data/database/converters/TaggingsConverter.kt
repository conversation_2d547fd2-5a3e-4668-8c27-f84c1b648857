package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class TaggingsConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<Tagging>? {
        val listType: Type = object : TypeToken<List<Tagging>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(taggings: List<Tagging>?): String {
        return gson.toJson(taggings)
    }
}