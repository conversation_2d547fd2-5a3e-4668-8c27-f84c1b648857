package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiPosService
import com.eatapp.clementine.data.network.response.pos.PosRecordResponse
import com.eatapp.clementine.data.network.response.pos.PosRecordsResponse
import com.eatapp.clementine.internal.simpleDate
import java.util.Date
import javax.inject.Inject

class PosRepositoryImpl @Inject constructor(
    private val eatApiPosService: EatApiPosService
) : PosRepository {

    override suspend fun posRecord(posRecordId: String): PosRecordResponse
            = eatApiPosService.posRecordAsync(posRecordId)

    override suspend fun posRecords(date: Date): PosRecordsResponse
            = eatApiPosService.posRecordsAsync(simpleDate(date))
}