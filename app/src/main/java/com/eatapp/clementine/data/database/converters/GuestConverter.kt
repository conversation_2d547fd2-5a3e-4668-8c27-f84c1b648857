package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.guest.Guest
import com.google.gson.Gson

class GuestConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): Guest? {
        return gson.fromJson(value, Guest::class.java)
    }

    @TypeConverter
    fun toString(guest: Guest?): String? {
        return gson.toJson(guest)
    }
}