package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.notification.NotificationsResponse
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import com.eatapp.clementine.data.network.response.survey.SurveyData
import java.util.Date

interface ReservationsRepository {

    suspend fun reservations(date: Date): ReservationsResponse

    suspend fun reservation(restaurantId: String, eatId: String): ReservationResponse

    suspend fun updateReservation(restaurantId: String, reservationId: String, reservationBody: ReservationBody): ReservationResponse

    suspend fun updateReservationStatus(restaurantId: String, reservationId: String, reservationBody: StatusBody): ReservationResponse

    suspend fun updateVouchersForReservation(
        restaurantId: String,
        reservationId: String,
        body: AssignVouchersRequest
    ): ReservationResponse

    suspend fun redeemVoucherForReservation(
        restaurantId: String,
        reservationId: String,
        body: RedeemVoucherAssignmentRequest
    ): ReservationResponse

    suspend fun createReservation(reservationBody: ReservationBody): ReservationResponse

    suspend fun deleteReservation(reservationId: String)

    suspend fun surveys(): SurveyData

    suspend fun saveReservations(list: List<Reservation>)

    suspend fun getReservations(restaurantId: String, startDate: Date?, endDate: Date?): List<Reservation>

    suspend fun deleteReservations(restaurantId: String, startDate: Date?, endDate: Date?)

    suspend fun tableReady(reservationId: String): ReservationResponse

    suspend fun notifications(restaurantId: String, page: Int, limit: Int): NotificationsResponse
}