package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiPaymentsService
import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.PaymentResponse
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse
import javax.inject.Inject

enum class PaymentActionType(val path: String) {
    SEND_REMINDER("messages"),
    VOID("void"),
    CAPTURE("capture"),
    REFUND("refund"),
    CANCEL("cancel")
}

class PaymentsRepositoryImpl @Inject constructor(
    private val eatApiPaymentsService: EatApiPaymentsService
) : PaymentsRepository {

    override suspend fun loadPayment(paymentId: String): PaymentResponse {
        return eatApiPaymentsService.loadPayment(paymentId)
    }

    override suspend fun updatePayment(
        paymentId: String,
        actionType: PaymentActionType
    ): PaymentResponse {
        return eatApiPaymentsService.updatePayment(paymentId, actionType.path)
    }

    override suspend fun refundPayment(
        paymentId: String,
        refundAmount: Double,
        userId: String,
        pin: String?
    ): PaymentResponse {
        return eatApiPaymentsService.refundPayment(
            paymentId,
            refundAmount = refundAmount,
            userId = userId,
            pin = pin
        )
    }

    override suspend fun sendReminder(paymentId: String): MessagesResponse {
        return eatApiPaymentsService.sendReminder(paymentId)
    }

    override suspend fun loadPaymentRules(): PaymentRulesResponse {
        return eatApiPaymentsService.loadPaymentRules()
    }

    override suspend fun createPayment(createPaymentBody: CreatePaymentBody): PaymentResponse {
        return eatApiPaymentsService.createPayment(createPaymentBody)
    }

    override suspend fun editPayment(paymentId: String, editPaymentBody: EditPaymentBody): PaymentResponse {
        return eatApiPaymentsService.editPayment(paymentId, editPaymentBody)
    }
}