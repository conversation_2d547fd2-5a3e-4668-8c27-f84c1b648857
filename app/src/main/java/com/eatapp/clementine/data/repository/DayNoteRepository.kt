package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.response.daynote.DayNoteResponse
import com.eatapp.clementine.data.network.response.daynote.DayNotesResponse
import java.util.Date

interface DayNoteRepository {

    suspend fun dayNote(date: Date): DayNotesResponse

    suspend fun insertDayNote(date: Date, content: String): DayNoteResponse

    suspend fun updateDayNote(noteId: String, content: String): DayNoteResponse

}