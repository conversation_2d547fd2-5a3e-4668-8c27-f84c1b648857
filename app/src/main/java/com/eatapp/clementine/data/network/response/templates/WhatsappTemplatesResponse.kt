package com.eatapp.clementine.data.network.response.templates

import android.os.Parcelable
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

data class WhatsappTemplatesResponse(
    @SerializedName("data")
    val templates: List<WhatsappTemplate>
)

@Parcelize
data class WhatsappTemplate(
    @SerializedName("id")
    override val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: WhatsappTemplateAttributes
) : Template, Parcelable {

    override val name: String
        get() = attributes.name

    override val messageType: String
        get() = attributes.messageType

    override val body: String
        get() = attributes.components.body.text

    override val updatedAt: String
        get() = attributes.updatedAt

    override val channel: ChannelType
        get() = ChannelType.WHATSAPP

    @IgnoredOnParcel
    override var previewExpanded: Boolean = false

    @IgnoredOnParcel
    override var disabled: Boolean = false

    val status: String
        get() = attributes.status

    val language: String
        get() = attributes.language

    val category: String
        get() = attributes.category

    val components: TemplateComponents
        get() = attributes.components

    val connectedToEvent: Boolean
        get() = attributes.connectedToEvent

    val belongsToRestaurant: Boolean
        get() = attributes.belongsToRestaurant
}

@Parcelize
data class WhatsappTemplateAttributes(
    @SerializedName("status")
    val status: String,
    @SerializedName("message_type")
    val messageType: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("language")
    val language: String,
    @SerializedName("category")
    val category: String,
    @SerializedName("components")
    val components: TemplateComponents,
    @SerializedName("updated_at")
    val updatedAt: String,
    @SerializedName("connected_to_event")
    val connectedToEvent: Boolean,
    @SerializedName("belongs_to_restaurant")
    val belongsToRestaurant: Boolean
) : Parcelable

@Parcelize
data class TemplateComponents(
    @SerializedName("body")
    val body: ComponentText,
    @SerializedName("footer")
    val footer: ComponentText?,
    @SerializedName("header")
    val header: ComponentText?,
    @SerializedName("buttons")
    val buttons: List<TemplateButton>?
) : Parcelable

@Parcelize
data class TemplateButton(
    @SerializedName("text")
    val text: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("value")
    val value: String
) : Parcelable

@Parcelize
data class ComponentText(
    @SerializedName("text")
    val text: String,
    @SerializedName("type")
    val type: String
) : Parcelable