package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.RegisterBody
import com.eatapp.clementine.data.network.response.auth.AuthResponse
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldResponse
import com.eatapp.clementine.data.network.response.guest.GuestTagsResponse
import com.eatapp.clementine.data.network.response.pizzaslicer.PizzaSlicerResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantsResponse
import com.eatapp.clementine.data.network.response.room.RoomsResponse
import com.eatapp.clementine.data.network.response.room.TablesResponse
import com.eatapp.clementine.data.network.response.shift.ShiftsResponse
import com.eatapp.clementine.data.network.response.tag.TagsResponse
import com.eatapp.clementine.data.network.response.templates.MessageTemplatesResponse
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplatesResponse
import com.eatapp.clementine.data.network.response.user.UserResponse
import com.eatapp.clementine.data.network.response.user.UsersResponse
import com.eatapp.clementine.data.network.response.vouchers.VouchersResponse
import javax.inject.Inject

class FabricateRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : FabricateRepository {

    override suspend fun login(
        email: String,
        password: String,
        captchaToken: String
    ): AuthResponse = eatApiRestaurant.loginAsync(email, password, captchaToken)

    override suspend fun register(registerBody: RegisterBody): AuthResponse =
        eatApiRestaurant.registerAsync(registerBody)

    override suspend fun restaurants(): RestaurantsResponse =
        eatApiRestaurant.restaurantsAsync(true, 9999)

    override suspend fun restaurant(restaurantId: String): RestaurantResponse =
        eatApiRestaurant.restaurantAsync(restaurantId)

    override suspend fun guestTags(): GuestTagsResponse = eatApiRestaurant.guestTagsAsync()

    override suspend fun rooms(restaurantId: String): RoomsResponse =
        eatApiRestaurant.roomsAsync(restaurantId)

    override suspend fun tables(restaurantId: String): TablesResponse =
        eatApiRestaurant.tablesAsync(restaurantId, 9999)

    override suspend fun user(restaurantId: String, userId: String): UserResponse =
        eatApiRestaurant.userAsync(restaurantId, userId)

    override suspend fun users(restaurantId: String): UsersResponse =
        eatApiRestaurant.users(restaurantId)

    override suspend fun tags(restaurantId: String): TagsResponse = eatApiRestaurant.tagsAsync(9999)

    override suspend fun shifts(restaurantId: String): ShiftsResponse =
        eatApiRestaurant.shiftsAsync(restaurantId, "active", 999)

    override suspend fun pizzaSlicer(): PizzaSlicerResponse = eatApiRestaurant.pizzaSliceAsync()

    override suspend fun customFields(): CustomFieldResponse = eatApiRestaurant.customFields()

    override suspend fun twoFactor(token: String, otp: String): AuthResponse =
        eatApiRestaurant.loginTwoFactor("Bearer $token", otp)

    override suspend fun messageTemplates(restaurantId: String): MessageTemplatesResponse =
        eatApiRestaurant.messageTemplatesAsync()

    override suspend fun whatsappTemplates(restaurantId: String): WhatsappTemplatesResponse =
        eatApiRestaurant.whatsappTemplatesAsync()

    override suspend fun vouchers() = eatApiRestaurant.vouchers()
}

