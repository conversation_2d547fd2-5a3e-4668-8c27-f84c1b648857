package com.eatapp.clementine.data.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.eatapp.clementine.data.network.response.reservation.Reservation
import java.util.Date

@Dao
interface ReservationDao {

    @Query("SELECT * FROM reservation_table WHERE dbRestaurantId = :restaurantId AND dbStartTime BETWEEN :startDate AND :endDate")
    suspend fun getReservations(restaurantId: String, startDate: Date?, endDate: Date?): List<Reservation>

    @Query("DELETE FROM reservation_table WHERE dbRestaurantId = :restaurantId AND dbStartTime BETWEEN :startDate AND :endDate")
    suspend fun deleteReservations(restaurantId: String, startDate: Date?, endDate: Date?)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun saveReservations(list: List<Reservation>)

}