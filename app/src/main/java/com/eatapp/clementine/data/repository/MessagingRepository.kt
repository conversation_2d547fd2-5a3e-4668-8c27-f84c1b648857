package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.response.conversation.ConversationsResponse
import com.eatapp.clementine.data.network.response.message.MessagesResponse

interface MessagingRepository {

    suspend fun messages(
        restaurantId: String,
        guestId: String?,
        reservationId: String?
    ): MessagesResponse

    suspend fun sendSms(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any

    suspend fun sendEmail(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any  

    suspend fun sendWhatsapp(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any

    suspend fun markAsRead(
        restaurantId: String,
        guestId: String
    ): Any

    suspend fun conversations(): ConversationsResponse
}