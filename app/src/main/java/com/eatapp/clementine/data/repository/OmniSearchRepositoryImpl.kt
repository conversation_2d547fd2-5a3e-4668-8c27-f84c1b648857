package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchResponse
import javax.inject.Inject

class OmniSearchRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : OmniSearchRepository {

    override suspend fun search(query: String): OmniSearchResponse {
        return eatApiRestaurant.search(query)
    }
}