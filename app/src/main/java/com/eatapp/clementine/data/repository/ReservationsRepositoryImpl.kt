package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.notification.NotificationsResponse
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import com.eatapp.clementine.data.network.response.survey.SurveyData
import com.eatapp.clementine.internal.simpleDate
import java.util.*
import javax.inject.Inject

class ReservationsRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant,
    private val reservationDao: ReservationDao
) : ReservationsRepository {

    override suspend fun reservations(date: Date): ReservationsResponse =
        eatApiRestaurant.reservationsAsync(1000, simpleDate(date))

    override suspend fun reservation(restaurantId: String, eatId: String): ReservationResponse =
        eatApiRestaurant.reservationAsync(restaurantId, eatId)

    override suspend fun updateReservation(
        restaurantId: String,
        reservationId: String,
        reservationBody: ReservationBody
    ): ReservationResponse =
        eatApiRestaurant.updateReservationAsync(restaurantId, reservationId, reservationBody)

    override suspend fun updateReservationStatus(
        restaurantId: String,
        reservationId: String,
        reservationBody: StatusBody
    ): ReservationResponse =
        eatApiRestaurant.updateReservationStatusAsync(restaurantId, reservationId, reservationBody)

    override suspend fun updateVouchersForReservation(
        restaurantId: String,
        reservationId: String,
        body: AssignVouchersRequest
    ) = eatApiRestaurant.updateVouchersForReservation(restaurantId, reservationId, body)

    override suspend fun redeemVoucherForReservation(
        restaurantId: String,
        reservationId: String,
        body: RedeemVoucherAssignmentRequest
    ): ReservationResponse =
        eatApiRestaurant.redeemVoucherForReservation(restaurantId, reservationId, body)

    override suspend fun createReservation(reservationBody: ReservationBody): ReservationResponse =
        eatApiRestaurant.createReservationAsync(reservationBody)

    override suspend fun deleteReservation(reservationId: String) =
        eatApiRestaurant.deleteReservationAsync(reservationId)

    override suspend fun surveys(): SurveyData =
        eatApiRestaurant.surveysAsync()

    override suspend fun saveReservations(list: List<Reservation>) =
        reservationDao.saveReservations(list)

    override suspend fun getReservations(restaurantId: String, startDate: Date?, endDate: Date?): List<Reservation> =
        reservationDao.getReservations(restaurantId, startDate, endDate)

    override suspend fun deleteReservations(restaurantId: String, startDate: Date?, endDate: Date?) =
        reservationDao.deleteReservations(restaurantId, startDate, endDate)

    override suspend fun tableReady(reservationId: String): ReservationResponse =
        eatApiRestaurant.tableReady(reservationId)

    override suspend fun notifications(restaurantId: String, page: Int, limit: Int): NotificationsResponse =
        eatApiRestaurant.notificationsAsync(page = page)

}