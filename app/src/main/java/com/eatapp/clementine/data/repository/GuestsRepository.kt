package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse

interface GuestsRepository {

    suspend fun guests(query: String?, page: Int, limit: Int): GuestsResponse

    suspend fun updateGuest(guestId:String, guestBody: GuestBody): GuestResponse

    suspend fun createGuest(guestBody: GuestBody): GuestResponse

    suspend fun deleteGuest(guestId:String)

    suspend fun reservations(guestId: String): ReservationsResponse

    suspend fun updateVouchersForGuest(
        guestId: String,
        body: AssignVouchersRequest
    ): GuestResponse

    suspend fun redeemVoucherForGuest(
        guestId: String,
        body: RedeemVoucherAssignmentRequest
    ): GuestResponse
}