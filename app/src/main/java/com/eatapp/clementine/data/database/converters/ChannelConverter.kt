package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.reservation.Channel
import com.google.gson.Gson

class ChannelConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String?): Channel? {
        return gson.fromJson(value, Channel::class.java)
    }

    @TypeConverter
    fun toString(channel: Channel?): String? {
        return gson.toJson(channel)
    }
}