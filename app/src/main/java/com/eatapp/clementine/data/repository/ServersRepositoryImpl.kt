package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.response.server.ServerResponse
import com.eatapp.clementine.data.network.response.server.ServersResponse
import javax.inject.Inject

class ServersRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : ServersRepository {

    override suspend fun servers(): ServersResponse = eatApiRestaurant.serversAsync()

    override suspend fun updateServer(serverId: String, body: ServerBody): ServerResponse =
        eatApiRestaurant.updateServerAsync(serverId, body)
}