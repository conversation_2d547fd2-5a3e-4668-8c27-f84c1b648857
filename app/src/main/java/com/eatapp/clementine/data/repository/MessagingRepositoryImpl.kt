package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiMessaging
import com.eatapp.clementine.data.network.response.conversation.ConversationsResponse
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import javax.inject.Inject

class MessagingRepositoryImpl @Inject constructor(
    private val eatApiMessaging: EatApiMessaging
) : MessagingRepository {

    override suspend fun messages(restaurantId: String, guestId: String?, reservationId: String?): MessagesResponse =
        eatApiMessaging.messages(restaurantId, guestId, reservationId)

    override suspend fun sendSms(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any = eatApiMessaging.sendSms(restaurantId, reservationId, guestId, templateId, text)          

    override suspend fun sendEmail(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any = eatApiMessaging.sendEmail(restaurantId, reservationId, guestId, templateId, text)    

    override suspend fun sendWhatsapp(
        restaurantId: String,
        reservationId: String?,
        guestId: String?,
        templateId: String?,
        text: String?
    ): Any = eatApiMessaging.sendWhatsapp(restaurantId, reservationId, guestId, templateId, text) 

    override suspend fun markAsRead(restaurantId: String, guestId: String): Any =
        eatApiMessaging.markAsRead(restaurantId, guestId)

    override suspend fun conversations(): ConversationsResponse =
        eatApiMessaging.conversations() 
}