package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import javax.inject.Inject

class GuestsRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : GuestsRepository {

    override suspend fun guests(query: String?, page: Int, limit: Int): GuestsResponse =
        eatApiRestaurant.guestsAsync(query, page, limit)

    override suspend fun updateGuest(guestId: String, guestBody: GuestBody): GuestResponse =
        eatApiRestaurant.updateGuestAsync(guestId, guestBody)

    override suspend fun createGuest(guestBody: GuestBody): GuestResponse =
        eatApiRestaurant.createGuestAsync(guestBody)

    override suspend fun deleteGuest(guestId: String) =
        eatApiRestaurant.deleteGuestAsync(guestId)

    override suspend fun reservations(guestId: String): ReservationsResponse =
        eatApiRestaurant.guestReservationsAsync(1000, guestId)

    override suspend fun updateVouchersForGuest(
        guestId: String,
        body: AssignVouchersRequest
    ) = eatApiRestaurant.updateVouchersForGuest(guestId, body)

    override suspend fun redeemVoucherForGuest(
        guestId: String,
        body: RedeemVoucherAssignmentRequest
    ) = eatApiRestaurant.redeemVoucherForGuest(guestId, body)
}
