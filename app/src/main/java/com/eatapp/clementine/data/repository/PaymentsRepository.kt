package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.message.MessagesResponse
import com.eatapp.clementine.data.network.response.payment.PaymentResponse
import com.eatapp.clementine.data.network.response.payment.PaymentRulesResponse

interface PaymentsRepository {
    suspend fun loadPayment(paymentId: String): PaymentResponse
    suspend fun updatePayment(paymentId: String, actionType: PaymentActionType): PaymentResponse
    suspend fun refundPayment(paymentId: String, refundAmount: Double, userId: String, pin: String?): PaymentResponse
    suspend fun sendReminder(paymentId: String): MessagesResponse
    suspend fun loadPaymentRules(): PaymentRulesResponse
    suspend fun createPayment(createPaymentBody: CreatePaymentBody): PaymentResponse
    suspend fun editPayment(paymentId: String, editPaymentBody: EditPaymentBody): PaymentResponse
}