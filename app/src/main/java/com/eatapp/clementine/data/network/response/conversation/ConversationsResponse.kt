package com.eatapp.clementine.data.network.response.conversation

import com.eatapp.clementine.internal.eatDateISO8601Timezone1DateFormatter
import com.google.gson.annotations.SerializedName
import java.util.Date

data class ConversationsResponse(
    @SerializedName("data")
    val conversations: List<Conversation>
)

data class Conversation(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: ConversationAttributes
) {
    val guestId: String
        get() = attributes.guestId

    val phone: String?
        get() = attributes.phone

    val email: String?
        get() = attributes.email

    val content: String
        get() = attributes.content

    val profileImageUrl: String?
        get() = attributes.profileImageUrl

    val contactId: String
        get() = attributes.contactId

    var unreadMessagesCount: Int
        get() = attributes.unreadMessagesCount
        set(value) { attributes.unreadMessagesCount = value }

    val fullName: String
        get() = attributes.fullName

    val createdAt: Date?
        get() = attributes.createdAt.let { eatDateISO8601Timezone1DateFormatter().parse(it) }
}

data class ConversationAttributes(
    @SerializedName("guest_id")
    val guestId: String,
    @SerializedName("phone")
    val phone: String?,
    @SerializedName("email")
    val email: String?,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("content")
    val content: String,
    @SerializedName("profile_image_url")
    val profileImageUrl: String?,
    @SerializedName("contact_id")
    val contactId: String,
    @SerializedName("unread_messages_count")
    var unreadMessagesCount: Int,
    @SerializedName("full_name")
    val fullName: String
)