package com.eatapp.clementine.internal

object Constants {

    const val RESERVATION_EXTRA = "reservation"
    const val RESERVATION_KEY_EXTRA = "reservation_key"
    const val RESERVATION_ID_EXTRA = "reservation_id"
    const val POS_RECORD_EXTRA = "pos_record"
    const val GUEST_EXTRA = "guest"
    const val GUEST_EXTRA_FIRST_NAME = "guest_first_name"
    const val GUEST_EXTRA_LAST_NAME = "guest_last_name"
    const val GUEST_EXTRA_PHONE_NUMBER = "guest_phone_number"
    const val GUEST_EXTRA_EMAIL = "guest_email"
    const val GUEST_EXTRA_NEW_FLOW = "guest_new_flow"
    const val GUEST_EXTRA_RESERVATION_ID = "guest_reservation_id"
    const val NOTES_EXTRA = "notes"
    const val TABLE_ID_EXTRA = "table_id"
    const val RESTAURANT_ID_EXTRA = "restaurant_id"
    const val TABLES_EXTRA = "tables"
    const val CLOSINGS_EXTRA = "closings"
    const val RESERVATIONS_EXTRA = "reservations"
    const val TITLE_EXTRA = "title"
    const val SUB_TITLE_EXTRA = "sub_title"
    const val GUEST_SIMPLIFIED_MODE_EXTRA = "simplified_mode"
    const val GUEST_CREATE_MODE_EXTRA = "guest_create_mode"
    const val CAROUSEL_TITLE_EXTRA = "title"
    const val CAROUSEL_DESC_EXTRA = "desc"
    const val CAROUSEL_IMAGE_EXTRA = "image"
    const val STATUS_TYPE = "status_type"
    const val WEBVEW_URL = "webview_url"
    const val DATE = "date"
    const val IS_WAITLIST = "is_waitlist"
    const val PRINT_MODE = "print_mode"
    const val INITIAL_STATUS_EXTRA = "initial_status"
    const val SELECTED_COUNTRY_CODE_EXTRA = "selected_country"
    const val TEMPLATE_EXTRA = "selected_template"
    const val STATUS = "status"
    const val NOTIFICATION_ID_EXTRA = "notification_id"


    const val RESERVATION_REQUEST = 77
    const val GUEST_REQUEST = 99
    const val NOTES_REQUEST = 88
    const val RESTAURANTS_REQUEST = 66

    const val NOTES_RESULT = 71
    const val TABLES_RESULT = 72

    const val GUEST_RESULT_UPDATED = 91
    const val GUEST_RESULT_DELETED = 92
    const val GUEST_RESULT_ADDED = 93
    const val GUEST_RESULT_NONE = 94

    const val RESERVATION_RESULT_UPDATED = 191
    const val RESERVATION_RESULT_DELETED = 192
    const val RESERVATION_RESULT_ADDED = 193
    const val RESERVATION_RESULT_NONE = 194

    const val KEY_IS_HTML = "KEY_HTML"
    const val KEY_IS_REPORTS = "KEY_REPORTS"
    const val KEY_NAVIGATION_CATEGORY_ICON = "KEY_NAVIGATION_CATEGORY_NAME"
    const val KEY_NAVIGATION_ITEM_NAME = "KEY_NAVIGATION_ITEM_NAME"
    const val GUEST_CUSTOM = "guest_custom"
    const val DEMO = "demo"

    const val RESTAURANT_ID_HEADER = "X-Restaurant-ID"
}