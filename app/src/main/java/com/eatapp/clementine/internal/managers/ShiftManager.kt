package com.eatapp.clementine.internal.managers

import android.text.format.DateFormat
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.shift.Shift
import com.eatapp.clementine.internal.dateFromString
import com.eatapp.clementine.internal.endOfTheEatDay
import com.eatapp.clementine.internal.startOfTheDay
import com.eatapp.clementine.internal.startOfTheDayAbsolute
import com.eatapp.clementine.internal.startOfTheEatDay
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ShiftManager @Inject constructor() {

    companion object {
        private const val SHIFT_TYPE_RECURRING = "recurring"
        private const val SHIFT_TYPE_EVENT = "event"
        private const val EAT_START_OF_THE_DAY = 14400 // 04:00 AM current day
        private const val EAT_END_OF_THE_DAY = 99900 // 03:45 AM next day
        private const val FIFTEEN_MINUTES = 900 // 15 minutes
        private const val START_OF_DAY_ABSOLUTE = 0 // 00:00
        private const val END_OF_DAY_ABSOLUTE = 86400 // 24:00
    }

    private var shifts: MutableMap<String,List<Shift>> = HashMap()

    fun shifts(restaurantId: String, s: List<Shift>) {
        shifts[restaurantId] = s
    }

    fun shifts(restaurantId: String): List<Shift>? {
        return shifts[restaurantId]
    }

    fun shift(restaurantId: String, reservation: Reservation): Shift? {

        return shiftsForTheDay(restaurantId, reservation.startTime).find {

            val day: String = DateFormat
                .format("EEEE", reservation.startTime).toString().lowercase(Locale.ROOT)

            val shiftStartDate = dateFromString(it.startDate)
            var shiftEndDate = dateFromString(it.endDate)

            val c: Calendar = Calendar.getInstance()
            shiftEndDate?.run {
                c.time = this
                c.add(Calendar.DATE, 1)
                shiftEndDate = c.time
            }

            val activeForCurrentDay: Boolean = (it.shiftType == SHIFT_TYPE_EVENT
                    && (shiftStartDate == null || reservation.startTime.after(shiftStartDate))
                    && (shiftEndDate == null || reservation.startTime.before(shiftEndDate)))
                    || (it.shiftType == SHIFT_TYPE_RECURRING
                    && (shiftStartDate == null || reservation.startTime.after(shiftStartDate))
                    && (shiftEndDate == null || reservation.startTime.before(shiftEndDate))
                    && it.daysOfWeek.contains(day))


            if (activeForCurrentDay && it.status == "active") {

                val dayStart: Date = startOfTheDay(reservation.startTime)

                val diffInMs: Long = reservation.startTime.time - dayStart.time
                val resFirstSeating: Long = TimeUnit.MILLISECONDS.toSeconds(diffInMs)

                resFirstSeating >= it.firstSeating && resFirstSeating <= it.lastSeating

            } else {
                false
            }
        }
    }

    fun shiftsForTheDay(restaurantId: String, date: Date): MutableList<Shift> {

        val dayStart: Date = startOfTheEatDay(date)
        val dayEnd: Date = endOfTheEatDay(date)

        val filteredShifts = shifts[restaurantId]?.filter {

            val day: String = DateFormat
                .format("EEEE", date).toString().lowercase(Locale.ROOT)

            val shiftStartDate: Date = shiftStartTime(it, dayStart)
            val shiftEndDate: Date = shiftEndTime(it, dayStart)

            val activeForCurrentDay: Boolean = (it.shiftType == SHIFT_TYPE_EVENT
                    && dayStart.before(shiftEndDate)
                    && dayEnd.after(shiftStartDate))
                    || (it.shiftType == SHIFT_TYPE_RECURRING
                    && dayStart.before(shiftEndDate)
                    && dayEnd.after(shiftStartDate)
                    && it.daysOfWeek.contains(day))

            activeForCurrentDay && it.status == "active"

        }?.toMutableList()

        val shifts: MutableList<Shift> = mutableListOf()

        for (i in EAT_START_OF_THE_DAY..(EAT_END_OF_THE_DAY - 500) step FIFTEEN_MINUTES) {

            var timeslot = i
            if (timeslot >= END_OF_DAY_ABSOLUTE) {
                timeslot -= END_OF_DAY_ABSOLUTE
            }

            var isFirstSeatingAvailable = false
            var isSameShiftFound = false
            var tempShift: Shift? = null

            filteredShifts?.forEach lit@{ shift ->

                val isAlreadyAddedShiftEventShift = shifts.lastOrNull() != null &&
                        shifts.lastOrNull()!!.shiftType == SHIFT_TYPE_EVENT && i < shifts.lastOrNull()!!.lastSeating

                if ((shift.firstSeating == timeslot && !isAlreadyAddedShiftEventShift) ||
                    (timeslot > shift.firstSeating && timeslot < shift.lastSeating) &&
                    (shifts.lastOrNull() == null || i > shifts.lastOrNull()!!.lastSeating)
                ) {

                    if (shifts.lastOrNull() != null && shift.id == shifts.lastOrNull()!!.id) {
                        isSameShiftFound = true
                        return@lit
                    }

                    isFirstSeatingAvailable = true
                    tempShift = shift.clone()
                    (tempShift as Shift).firstSeating = i

                    val increment =
                        if (shift.firstSeating < EAT_START_OF_THE_DAY && shift.lastSeating > EAT_START_OF_THE_DAY) {
                            shift.lastSeating - EAT_START_OF_THE_DAY
                        } else {
                            shift.lastSeating - shift.firstSeating
                        }
                    tempShift?.lastSeating = i + increment

                    return@lit
                }
            }

            if (isSameShiftFound) {
                continue
            }

            if (isFirstSeatingAvailable) {
                if (shifts.size > 0) {
                    shifts.lastOrNull()?.lastSeating =
                        (tempShift?.firstSeating ?: 0) - FIFTEEN_MINUTES
                }

                shifts.add(tempShift!!)

            } else {

                if (shifts.size == 0 || (shifts.lastOrNull()?.id != "empty" && (shifts.lastOrNull()?.lastSeating ?: 0) < i)
                ) {
                    val emptyShift = Shift("Out of shift", "empty", firstSeating = i, color = "#b5b5b5", fakeId = shifts.size.toString())
                    shifts.add(emptyShift)
                }
            }
        }

        if (shifts.size > 0) {
            shifts.lastOrNull()?.lastSeating = EAT_END_OF_THE_DAY
        }

        return shifts
    }

    private fun shiftStartTime(shift: Shift, dayStart: Date): Date {
        val shiftStartDate = prepareShiftTime(shift.startDate, dayStart)

        // Add shift start time
        offsetShiftTime(shiftStartDate, shift.firstSeating)

        return shiftStartDate.time
    }

    private fun shiftEndTime(shift: Shift, dayStart: Date): Date {
        val shiftEndDate = prepareShiftTime(shift.endDate, dayStart)

        offsetShiftTime(shiftEndDate, shift.lastSeating)

        return shiftEndDate.time
    }

    private fun offsetShiftTime(
        shiftDate: Calendar,
        seatingTime: Int
    ) {
        shiftDate.add(Calendar.SECOND, seatingTime)
        if (seatingTime < EAT_START_OF_THE_DAY /*shift spills over to the next day 00-24h*/) {
            shiftDate.add(Calendar.DAY_OF_MONTH, 1)
        }
    }

    private fun prepareShiftTime(
        shiftDate: String?,
        dayStart: Date
    ): Calendar {
        val shiftStartDate = if (shiftDate == null /* recurring shift*/) {
            // Get day at 00:00:00
            startOfTheDayAbsolute(dayStart)
        } else {
            // Get shift day at 00:00:00
            val calendar = Calendar.getInstance()
            calendar.time = dateFromString(shiftDate)!!
            calendar
        }
        return shiftStartDate
    }

    fun createShiftsGroups(restaurantId: String, reservations: List<Reservation>, date: Date?): MutableList<Shift> {

        if (date == null) return mutableListOf()

        val shiftsForTheDay: MutableList<Shift> = shiftsForTheDay(restaurantId, date)

        val dayStart: Date = startOfTheDay(date)

        reservations.forEach { res ->

            val diffInMs: Long = res.startTime.time - dayStart.time
            val resFirstSeating: Long = TimeUnit.MILLISECONDS.toSeconds(diffInMs)

            shiftsForTheDay.forEach { shift ->

                if (resFirstSeating >= shift.firstSeating && resFirstSeating <= shift.lastSeating) {
                    shift.reservations?.add(res)
                }
            }
        }

        return shiftsForTheDay.filterNot {
            it.id == "empty"
                    && it.reservations?.isEmpty() == true
        }.toMutableList()
    }
}