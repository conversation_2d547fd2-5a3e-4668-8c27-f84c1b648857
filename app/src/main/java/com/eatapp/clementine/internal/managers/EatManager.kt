package com.eatapp.clementine.internal.managers

import android.content.SharedPreferences
import com.eatapp.clementine.data.network.response.IncludedResponse
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemCategory
import com.eatapp.clementine.data.network.response.auth.Auth
import com.eatapp.clementine.data.network.response.conversation.Conversation
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.data.network.response.product.Message
import com.eatapp.clementine.data.network.response.reservation.Channel
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.data.network.response.restaurant.ReservationStatus
import com.eatapp.clementine.data.network.response.restaurant.ReservationStatusCategory
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantsResponse
import com.eatapp.clementine.data.network.response.room.Room
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.server.Server
import com.eatapp.clementine.data.network.response.shift.Shift
import com.eatapp.clementine.data.network.response.tag.Category
import com.eatapp.clementine.data.network.response.tag.Tag
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.templates.MessageTemplate
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplate
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.network.response.user.User
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.enums.Configuration
import com.eatapp.clementine.enums.ReservationTag
import com.eatapp.clementine.internal.DateTypeAdapter
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.PosSection
import com.eatapp.clementine.internal.ReservationGroupSection
import com.eatapp.clementine.internal.ReservationSection
import com.eatapp.clementine.internal.ReservationsConfig
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.Status
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.collections.set
import androidx.core.content.edit
import java.util.Locale

const val TOKEN = "eat.token"
const val USER_ID = "eat.user.id"
const val USER_NAME = "eat.user.name"
const val USER_EMAIL = "eat.user.email"
const val USER_ROLE = "eat.user.role"
const val RESTAURANT_ID = "eat.restaurant.id"
const val WAIT_QUOTE = "eat.restaurant.wait.quote"
const val FIRST_START = "eat.restaurant.firststart"
const val HUBSPOT_TOKEN = "eat.restaurant.hubspot.token"
const val GROUP_BY = "eat.group.by"
const val GROUP_BY_ACTIVATED = "eat.group.by.activated"
const val SORT_BY = "eat.sort.by"
const val RESERVATION_COMPACT_MODE = "eat.reservation.compact.mode"
const val RESERVATION_DETAIL_MODE_TRACKED = "eat.reservation.detail.mode.tracked"
const val WHATSAPP_ALREADY_NOTIFIED = "eat.reservation.whatsapp_already_notified"
const val DAILY_NOTES_SEEN = "eat.daily_notes.seen"
const val NOTIFICATIONS_COUNT = "eat.restaurant.notifications.count"
const val SUPPRESSED_NOTIFICATION_IDS = "eat.suppressed.notification.ids"

const val manageAdmin = "manage_admin"
const val manageClosingPeriod = "manage_closing_period"
const val downloadCrm = "download_crm"
const val manageCrm = "manage_crm"
const val manageDayNotes = "manage_day_notes"
const val manageGuests = "manage_guests"
const val manageMessageTemplates = "manage_message_templates"
const val manageOnlineSeatingShifts = "manage_online_seating_shifts"
const val manageReservations = "manage_reservations"
const val manageTables = "manage_tables"
const val mergeGuests = "merge_guests"
const val overbookTimeslots = "overbook_timeslots"
const val requireCustomTag = "require_custom_tag"
const val tableSuggestions = "table_suggestions"
const val waitListTimer = "wait_list_timer"

const val showChitPrintConfigKey = "show_chit_print_config"
const val chitPrintConfigReservationNotes = "chit_config.reservation_notes"
const val chitPrintConfigReservationTags = "chit_config.reservation_tags"
const val chitPrintConfigGuestTags = "chit_config.guest_tags"
const val chitPrintConfigPrivateComments = "chit_config.private_comments"
const val chitPrintConfigPaymentDetails = "chit_config.payment_details"
const val chitPrintConfigNoOfVisits = "chit_config.no_of_visits"
const val chitPrintConfigAverageSpendPerGuest = "chit_config.average_spend_per_guest"
const val chitPrintConfigAverageReviewRating = "chit_config.average_review_rating"
const val chitPrintConfigLastReviewRating = "chit_config.last_review_rating"
const val chitPrintConfigRoomNumber = "chit_config.room_number"
const val printWhenGuestSeated = "print_when_guest_seated"

const val fieldOptionHide = "hide"
const val fieldOptionWarning = "warning"
const val fieldOptionError = "error"
const val fieldOptionTimerUp = "up_timer"
const val fieldOptionTimerDown = "down_timer"

const val includedTypeGuest = "guest"
const val includedTypeTagging = "tagging"
const val includedTypePayment = "payment"
const val includedTypeChannel = "channel"
const val includedTypeVoucherAssignment = "voucher_assignment"
const val includedTypeGroupRestaurant = "group_restaurant"


enum class TagType(val type: String) {
    Reservation("reservation"),
    Guest("guest"),
    Payment("payment")
}

enum class OptionType(val type: String) {
    Hide(fieldOptionHide),
    Warning(fieldOptionWarning),
    Error(fieldOptionError)
}

enum class TimerType(val type: String) {
    Hide(fieldOptionHide),
    Up(fieldOptionTimerUp),
    Down(fieldOptionTimerDown)
}

@Singleton
class EatManager @Inject constructor(
    private val sharedPreferences: SharedPreferences,
    private val featureFlagsManager: FeatureFlagsManager,
    private val shiftManager: ShiftManager,
    val reservationsConfig: ReservationsConfig,
    dateTypeAdapter: DateTypeAdapter
) {
    private var restaurants: List<Restaurant>? = null
    private var restaurant: Restaurant? = null
    private var restaurantId: String? = null
    private var rooms: MutableMap<String, List<Room>> = HashMap()
    private var tables: MutableMap<String, List<Table>> = HashMap()
    private var users: MutableMap<String, List<User>> = HashMap()
    private var user: MutableMap<String, User> = HashMap()
    private var tags: MutableMap<String, List<Tag>> = HashMap()
    private var servers: MutableMap<String, List<Server>> = HashMap()
    private var customFields: MutableMap<String, List<CustomField>> = HashMap()
    private var messageTemplates: MutableMap<String, List<MessageTemplate>> = HashMap()
    private var whatsappTemplates: MutableMap<String, List<WhatsappTemplate>> = HashMap()
    private var conversations: MutableMap<String, List<Conversation>> = HashMap()
    private var vouchers: MutableMap<String, List<Voucher>> = HashMap()

    private var token: String
    private var userId: String
    private var userName: String
    private var userEmail: String
    private var userRole: String

    private var countries: List<Country> = mutableListOf()

    private val gson: Gson

    val openedReservations: MutableMap<String, Date> = mutableMapOf()

    var reservationCompactMode: Boolean
        get() {
            return sharedPreferences.getBoolean(RESERVATION_COMPACT_MODE, false)
        }
        set(value) {
            sharedPreferences.edit {
                putBoolean(RESERVATION_COMPACT_MODE, value)
            }
        }

    var reservationDetailModeTracked: Boolean
        get() {
            return sharedPreferences.getBoolean(RESERVATION_DETAIL_MODE_TRACKED, false)
        }
        set(value) {
            sharedPreferences.edit {
                putBoolean(RESERVATION_DETAIL_MODE_TRACKED, value)
            }
        }

    val takers: List<User>
        get() {
            return users()?.filter { it.taker } ?: emptyList()
        }

    var navItems: List<NavigationItemCategory> = emptyList()

    init {
        token = sharedPreferences.getString(TOKEN, "")!!
        userId = sharedPreferences.getString(USER_ID, "")!!
        userName = sharedPreferences.getString(USER_NAME, "")!!
        userEmail = sharedPreferences.getString(USER_EMAIL, "")!!
        userRole = sharedPreferences.getString(USER_ROLE, "")!!
        restaurantId = sharedPreferences.getString(RESTAURANT_ID, "")
        val builder = GsonBuilder()
            .registerTypeAdapter(Date::class.java, dateTypeAdapter)
            .serializeNulls()
        gson = builder.create()
    }

    fun flush() {
        token("")
        userId("")
        userName("")
        userEmail("")
        userRole("")
        restaurantId("")
        restaurant = null
        restaurants = null
    }

    fun userData(auth: Auth) {
        token(auth.token!!)
        userId(auth.id)
        userName(auth.name)
        userEmail(auth.email)
        userRole(auth.role)
    }

    fun token(): String {
        return token
    }

    private fun token(token: String) {
        sharedPreferences.edit {
            putString(TOKEN, token)
        }
        this.token = token
    }

    fun userId(): String {
        return userId
    }

    private fun userId(id: String) {
        sharedPreferences.edit {
            putString(USER_ID, id)
        }
        this.userId = id
    }

    fun userName(): String {
        return userName
    }

    private fun userName(name: String) {
        sharedPreferences.edit {
            putString(USER_NAME, name)
        }
        this.userName = name
    }

    fun userEmail(): String {
        return userEmail
    }

    private fun userEmail(email: String) {
        sharedPreferences.edit {
            putString(USER_EMAIL, email)
        }
        this.userEmail = email
    }

    private fun userRole(): String {
        return userRole
    }

    private fun userRole(role: String) {
        sharedPreferences.edit {
            putString(USER_ROLE, role)
        }
        this.userRole = role
    }

    fun firstStart(): Boolean {
        return sharedPreferences.getBoolean(FIRST_START, true)
    }

    fun firstStart(firstStart: Boolean) {
        sharedPreferences.edit {
            putBoolean(FIRST_START, firstStart)
        }
    }

    fun waitQuote(): Int {
        return sharedPreferences.getInt(WAIT_QUOTE, 0)
    }

    fun waitQuote(waitQuote: Int) {
        sharedPreferences.edit {
            putInt(WAIT_QUOTE, waitQuote)
        }
    }

    var notificationsCount: Int
        get() {
            return sharedPreferences.getInt(NOTIFICATIONS_COUNT, 0)
        }
        set(value) {
            sharedPreferences.edit {
                putInt(NOTIFICATIONS_COUNT, value)
            }
        }

    fun hubspotToken(): String? {
        return sharedPreferences.getString(HUBSPOT_TOKEN + userEmail(), null)
    }

    fun hubspotToken(token: String) {
        sharedPreferences.edit {
            putString(HUBSPOT_TOKEN + userEmail(), token)
        }
    }

    fun restaurants(): List<Restaurant>? {
        return restaurants
    }

    var showChitPrintConfig: Boolean
        get() {
            return sharedPreferences.getBoolean(showChitPrintConfigKey + restaurantId, true)
        }
        set(value) {
            sharedPreferences.edit {
                putBoolean(showChitPrintConfigKey + restaurantId, value)
            }
        }

    var printWhenSeated: Boolean
        get() {
            return sharedPreferences.getBoolean(printWhenGuestSeated + restaurantId, false)
        }
        set(value) {
            sharedPreferences.edit {
                putBoolean(printWhenGuestSeated + restaurantId, value)
            }
        }

    fun chitPrintConfig(key: String, defaultValue: Boolean): Boolean {
        return sharedPreferences.getBoolean(key + restaurantId, defaultValue)
    }

    fun setChitPrintConfig(key: String, newValue: Boolean) {
        sharedPreferences.edit {
            putBoolean(key + restaurantId, newValue)
        }
    }

    fun restaurants(restaurantsResponse: RestaurantsResponse) {
        restaurants = restaurantsResponse.data.sortedBy { it.name.trim().first().lowercaseChar() }
    }

    fun restaurant(): Restaurant? {
        return restaurant
    }

    fun isWhatsappUserAlreadyNotified(reservationId: String): Boolean {
        return sharedPreferences.getBoolean(WHATSAPP_ALREADY_NOTIFIED + reservationId, false)
    }

    fun whatsappUserAlreadyNotified(reservationId: String, notified: Boolean) {
        sharedPreferences.edit {
            putBoolean(WHATSAPP_ALREADY_NOTIFIED + reservationId, notified)
        }
    }

    fun getSuppressedNotificationIds(): Set<String> {
        return sharedPreferences.getStringSet(SUPPRESSED_NOTIFICATION_IDS, emptySet()) ?: emptySet()
    }

    fun addSuppressedNotificationId(reservationId: String) {
        val current = getSuppressedNotificationIds().toMutableSet()
        current.add(reservationId)
        sharedPreferences.edit {
            putStringSet(SUPPRESSED_NOTIFICATION_IDS, current)
        }
    }

    fun removeSuppressedNotificationIds() {
        sharedPreferences.edit {
            putStringSet(SUPPRESSED_NOTIFICATION_IDS, emptySet())
        }
    }

    fun restaurant(restaurantResponse: RestaurantResponse) {
        restaurant = restaurantResponse.data
        restaurantId(restaurantResponse.data.id)
        statuses(restaurantResponse.data.statusCategories)
    }

    fun restaurantId(): String {
        return if (!restaurantId.isNullOrEmpty()) {
            restaurantId!!
        } else if (restaurant != null) {
            restaurant?.id!!
        } else if (restaurants?.isNotEmpty() == true) {
            restaurants?.get(0)?.id!!
        } else {
            ""
        }
    }

    private fun restaurantId(id: String) {
        sharedPreferences.edit {
            putString(RESTAURANT_ID, id)
        }
        restaurantId = id
    }

    private fun statuses(statuses: List<ReservationStatusCategory>) {
        Status.categories = statuses
    }

    fun user(restaurantId: String = restaurantId()): User? {
        return user[restaurantId]
    }

    fun user(restaurantId: String = restaurantId(), user: User) {
        this.user[restaurantId] = user
    }

    fun rooms(restaurantId: String = restaurantId()): List<Room>? {
        return rooms[restaurantId]
    }

    fun rooms(restaurantId: String = restaurantId(), rooms: List<Room>) {
        this.rooms[restaurantId] = rooms
    }

    fun tables(restaurantId: String = restaurantId()): List<Table>? {
        return tables[restaurantId]
    }

    fun tables(restaurantId: String = restaurantId(), tables: List<Table>) {
        this.tables[restaurantId] = tables
    }

    fun users(restaurantId: String = restaurantId()): List<User>? {
        return users[restaurantId]
    }

    fun users(restaurantId: String = restaurantId(), users: List<User>) {
        this.users[restaurantId] = users
    }

    fun tags(restaurantId: String = restaurantId()): List<Tag>? {
        return tags[restaurantId]
    }

    fun tags(restaurantId: String = restaurantId(), tags: List<Tag>) {
        this.tags[restaurantId] = tags
    }

    fun customFields(restaurantId: String = restaurantId()): List<CustomField>? {
        return customFields[restaurantId]
    }

    fun customFields(restaurantId: String = restaurantId(), customFields: List<CustomField>) {
        this.customFields[restaurantId] = customFields
    }

    fun servers(restaurantId: String = restaurantId()): List<Server>? {
        return servers[restaurantId]
    }

    fun servers(restaurantId: String = restaurantId(), servers: List<Server>) {
        this.servers[restaurantId] = servers
    }

    fun messageTemplates(restaurantId: String = restaurantId()): List<MessageTemplate>? {
        return messageTemplates[restaurantId]
    }

    fun messageTemplates(restaurantId: String = restaurantId(), templates: List<MessageTemplate>) {
        this.messageTemplates[restaurantId] = templates
    }

    fun whatsappTemplates(restaurantId: String = restaurantId()): List<WhatsappTemplate>? {
        return whatsappTemplates[restaurantId]
    }

    fun whatsappTemplates(restaurantId: String = restaurantId(), templates: List<WhatsappTemplate>) {
        this.whatsappTemplates[restaurantId] = templates
    }

    fun conversations(restaurantId: String = restaurantId()): List<Conversation>? {
        return conversations[restaurantId]
    }

    fun conversations(restaurantId: String = restaurantId(), conversations: List<Conversation>) {
        this.conversations[restaurantId] = conversations
    }

    fun vouchers(restaurantId: String = restaurantId()): List<Voucher>? {
        return vouchers[restaurantId]
    }

    fun vouchers(restaurantId: String = restaurantId(), vouchers: List<Voucher>) {
        this.vouchers[restaurantId] = vouchers
    }

    fun findTaker(): String? {

        takers.forEach {
            if (it.id == userId()) {
                return it.name
            }
        }

        return null
    }

    // Insert or update reservation's last opened timestamp
    fun insertOpenedReservation(id: String, timestamp: Date) {
        openedReservations[id] = timestamp
    }

    fun processReservations(response: ReservationsResponse) {

        response.reservations.forEach { reservation ->
            processReservation(reservation, response.included)
        }
    }

    fun processReservation(reservation: Reservation, included: List<IncludedResponse>) {

        // Added to always process guests first, so adding tagging to guests is available
        val includedSorted = included.sortedWith(compareBy { it.type != includedTypeGuest })

        includedSorted.forEach {
            when (it.type) {
                includedTypeChannel -> {
                    if (reservation.relationships?.channel?.data?.id == it.id) {
                        reservation.channel =
                            gson.fromJson(gson.toJson(it), Channel::class.java)
                    }
                }

                includedTypeGuest -> {
                    if (reservation.relationships?.guest?.data?.id == it.id) {
                        reservation.guest = gson.fromJson(gson.toJson(it), Guest::class.java)
                    }
                }

                includedTypeTagging -> {
                    if (reservation.relationships?.taggings?.data?.find { t -> t.id == it.id } != null) {
                        gson.fromJson(gson.toJson(it), Tagging::class.java)
                            ?.let { tagging ->
                                tagging.color = colorForTag(tagging.name, TagType.Reservation)
                                if (reservation.taggings == null) {
                                    reservation.taggings = mutableListOf()
                                }
                                reservation.taggings?.add(tagging)
                            }
                    }

                    reservation.guest?.let { guest ->
                        processGuestTags(guest, it)
                    }
                }

                includedTypePayment -> {
                    if (reservation.relationships?.payments?.data?.find { t -> t.id == it.id } != null) {
                        gson.fromJson(gson.toJson(it), Payment::class.java)
                            ?.let { payment ->
                                if (reservation.paymentsList == null) {
                                    reservation.paymentsList = mutableListOf()
                                }
                                reservation.paymentsList?.add(payment)
                            }
                    }
                }

                includedTypeVoucherAssignment -> {
                    if (reservation.relationships?.voucherAssignments?.data?.find { t -> t.id == it.id } != null) {
                        gson.fromJson(gson.toJson(it), VoucherAssignment::class.java)
                            ?.let { voucherAssignment ->
                                if (reservation.voucherAssignments == null) {
                                    reservation.voucherAssignments = mutableListOf()
                                }
                                reservation.voucherAssignments?.add(voucherAssignment)
                            }
                    }

                    reservation.guest?.let { guest ->
                        processGuestVoucherAssignments(guest, it)
                    }
                }
            }
        }

        reservation.tables = tables(reservation.attributes.restaurantId)?.filter { table ->
            reservation.relationships?.tables?.data?.any {
                it.id == table.id
            } ?: false
        }

        reservation.guest?.unreadMessagesCount = conversations()?.find { conversation ->
            conversation.guestId == reservation.guest?.id
        }?.unreadMessagesCount ?: 0

        reservation.dbStartTime = reservation.startTime
        reservation.lastOpenedAt = openedReservations[reservation.id] ?: reservation.updatedAt
        reservation.dbRestaurantId = restaurantId()
    }

    fun processGuests(guestsResponse: GuestsResponse) {
        guestsResponse.guests.forEach {
            processGuest(it, guestsResponse.included)
        }
    }

    fun processGuest(guest: Guest, included: List<IncludedResponse>) {
        included.forEach {
            if (it.type == includedTypeTagging) {
                processGuestTags(guest, it)
            } else if (it.type == includedTypeVoucherAssignment) {
                processGuestVoucherAssignments(guest, it)
            }
        }
    }

    private fun processGuestTags(guest: Guest, included: IncludedResponse) {
        if (guest.relationships?.taggings?.data?.find { t -> t.id == included.id } != null) {
            val tagging = gson.fromJson(gson.toJson(included), Tagging::class.java)
            tagging.color = colorForTag(tagging.name, TagType.Guest)
            if (guest.taggings == null) {
                guest.taggings = mutableListOf()
            }
            guest.taggings?.add(tagging)
        }
    }

    private fun processGuestVoucherAssignments(guest: Guest, included: IncludedResponse) {
        if (guest.relationships?.voucherAssignments?.data?.find { t -> t.id == included.id } != null) {
            gson.fromJson(gson.toJson(included), VoucherAssignment::class.java)
                ?.let { voucherAssignment ->
                    if (guest.voucherAssignments == null) {
                        guest.voucherAssignments = mutableListOf()
                    }
                    guest.voucherAssignments?.add(voucherAssignment)
                }
        }
    }

    private fun createPosLifecycleGroups(posRecords: List<PosRecord?>?): List<Pair<String, List<PosRecord?>?>> {

        val statuses: MutableList<Pair<List<ReservationStatus>, String>> = posLifecycleGroups()

        return statuses.map {
            val rs = posRecords?.filter { pr ->
                it.first.find { s -> s.code == pr?.reservation?.status }?.code == pr?.reservation?.status
            }
            Pair(it.second, rs)
        }.filter { (it.second?.count() ?: 0) > 0 }
    }

    private fun posLifecycleGroups(): MutableList<Pair<List<ReservationStatus>, String>> {
        val statuses: MutableList<Pair<List<ReservationStatus>, String>> = mutableListOf()
        statuses.add(Pair(Status.categoryForLifeCycle(Status.CategoriesLifeCycle.IN_SERVICE)?.statuses ?: emptyList(), "In-Service"))
        statuses.add(Pair(Status.categoryForLifeCycle(Status.CategoriesLifeCycle.REMOVED)?.statuses?.filter { Status.isFinished(it.code) } ?: emptyList(), "Finished"))
        return statuses
    }

    fun tagSelectors(taggings: List<Tagging>?, tagType: TagType): MutableList<ItemsGroup> {

        val groups: MutableList<ItemsGroup> = mutableListOf()

        val categories = tags(restaurantId())?.filter {
            it.context.contains(tagType.type)
        }?.map {
            it.category
        }?.distinctBy {
            it?.id
        }?.sortedBy {
            it?.name
        }

        categories?.map { category ->

            category?.let {

                val items: MutableList<SelectorItem> = mutableListOf()

                tags(restaurantId())?.sortedBy { it.importance }?.forEach { tag ->

                    if (tag.context.contains(tagType.type) && tag.category?.name == category.name) {

                        val item = SelectorItem(
                            "", tag.name, tag, icon = ReservationTag.getIcon(
                                tag.icon ?: ""
                            ), color = 0,
                            isSelected = false, isHeader = false, isDisabled = false
                        )

                        item.isSelected = taggings?.find { it.name == item.name } != null

                        items.add(item)
                    }
                }

                category.advanceTags = items

                groups.add(
                    ItemsGroup(
                        category.name, category.color, category.advanceTags,
                        checkable = false, checked = false
                    )
                )
            }
        }

        val uncategorised = Category("Uncategorised", "#808080")

        val items: MutableList<SelectorItem> = mutableListOf()

        tags(restaurantId())?.sortedBy { it.importance }?.forEach { tag ->

            if (tag.context.contains(tagType.type) && tag.category == null) {

                val item = SelectorItem(
                    "", tag.name, tag, icon = ReservationTag.getIcon(
                        tag.icon ?: ""
                    ), color = 0,
                    isSelected = false, isHeader = false, isDisabled = false
                )

                item.isSelected = taggings?.find { it.name == item.name } != null

                items.add(item)
            }
        }

        uncategorised.advanceTags = items

        if (uncategorised.advanceTags.isNotEmpty()) groups.add(
            ItemsGroup(
                uncategorised.name!!,
                uncategorised.color, uncategorised.advanceTags, checkable = false, checked = false
            )
        )

        return groups
    }

    fun taggingSelectors(addItem: Boolean, taggings: List<Tagging>?): MutableList<SelectorItem>? {

        var tags: MutableList<SelectorItem> = mutableListOf()

        taggings?.let {

            tags = taggings.map { tagging ->

                SelectorItem(
                    "",
                    tagging.name,
                    tagging,
                    icon = ReservationTag.getIcon(tagging.icon ?: ""),
                    color = 0,
                    isSelected = false,
                    isHeader = false,
                    isDisabled = false
                )

            } as MutableList<SelectorItem>
        }

        if (addItem) {
            val tag = SelectorItem("Add tag", isAddItem = true)
            tags.add(0, tag)
        }

        return when (tags.size > 0) {
            true -> tags
            false -> null
        }
    }

    private fun colorForTag(tagging: String, tagType: TagType): String {

        val color = tags(restaurantId())?.find { tag ->
            tag.name == tagging && tag.context.contains(tagType.type)
        }?.category?.color

        return when (color == null) {
            true -> "#808080"
            false -> color
        }
    }

    fun addedTags(selectedTags: List<Tagging>?, currentTags: List<Tagging>?): List<String>? {

        return selectedTags?.filter { selectedTag ->

            currentTags?.forEach { currentTag ->
                if (selectedTag.name == currentTag.name) {
                    return@filter false
                }
            }

            return@filter true

        }?.filter {
            it.id != ""
        }?.map {
            it.id
        }
    }

    fun removedTags(selectedTags: List<Tagging>?, currentTags: List<Tagging>?): List<String>? {

        return currentTags?.filter { currentTag ->

            selectedTags?.forEach { selectedTag ->
                if (selectedTag.name == currentTag.name) {
                    return@filter false
                }
            }

            return@filter true

        }?.filter {
            it.id != ""
        }?.map {
            it.id
        }
    }

    // Added for future support, if we introduce option to remove vouchers
    fun addedVoucherAssignments(
        updatedVouchers: MutableList<VoucherAssignment>?,
        initialVouchers: MutableList<VoucherAssignment>?
    ): List<String> {
        val currentIds = initialVouchers
            ?.map { it.id }
            ?.filter { it.isNotEmpty() }
            ?.toSet()
        return updatedVouchers
            ?.map { it.id }
            ?.filter { it.isNotEmpty() && currentIds?.contains(it) != true } ?: emptyList()
    }

    fun removedVoucherAssignments(
        updatedVouchers: MutableList<VoucherAssignment>?,
        initialVouchers: MutableList<VoucherAssignment>?
    ): List<String> {
        val selectedIds = updatedVouchers
            ?.map { it.id }
            ?.filter { it.isNotEmpty() }
            ?.toSet()

        return initialVouchers
            ?.map { it.id }
            ?.filter { it.isNotEmpty() && selectedIds?.contains(it) != true } ?: emptyList()
    }

    fun timeZones(): MutableList<SelectorItem> {

        val zones: MutableList<SelectorItem> = mutableListOf()

        val z = listOf(
            Pair("American Samoa", "(GMT-11:00) American Samoa"),
            Pair("International Date Line West", "(GMT-11:00) International Date Line West"),
            Pair("Midway Island", "(GMT-11:00) Midway Island"),
            Pair("Samoa", "(GMT-11:00) Samoa"),
            Pair("Hawaii", "(GMT-10:00) Hawaii"),
            Pair("Alaska", "(GMT-09:00) Alaska"),
            Pair("Pacific Time (US & Canada)", "(GMT-08:00) Pacific Time (US & Canada)"),
            Pair("Tijuana", "(GMT-08:00) Tijuana"),
            Pair("Arizona", "(GMT-07:00) Arizona"),
            Pair("Chihuahua", "(GMT-07:00) Chihuahua"),
            Pair("Mazatlan", "(GMT-07:00) Mazatlan"),
            Pair("Mountain Time (US & Canada)", "(GMT-07:00) Mountain Time (US & Canada)"),
            Pair("Central America", "(GMT-06:00) Central America"),
            Pair("Central Time (US & Canada)", "(GMT-06:00) Central Time (US & Canada)"),
            Pair("Guadalajara", "(GMT-06:00) Guadalajara"),
            Pair("Mexico City", "(GMT-06:00) Mexico City"),
            Pair("Monterrey", "(GMT-06:00) Monterrey"),
            Pair("Saskatchewan", "(GMT-06:00) Saskatchewan"),
            Pair("Bogota", "(GMT-05:00) Bogota"),
            Pair("Eastern Time (US & Canada)", "(GMT-05:00) Eastern Time (US & Canada)"),
            Pair("Indiana (East)", "(GMT-05:00) Indiana (East)"),
            Pair("Lima", "(GMT-05:00) Lima"),
            Pair("Quito", "(GMT-05:00) Quito"),
            Pair("Atlantic Time (Canada)", "(GMT-04:00) Atlantic Time (Canada)"),
            Pair("Caracas", "(GMT-04:00) Caracas"),
            Pair("Georgetown", "(GMT-04:00) Georgetown"),
            Pair("La Paz", "(GMT-04:00) La Paz"),
            Pair("Santiago", "(GMT-04:00) Santiago"),
            Pair("Newfoundland", "(GMT-03:30) Newfoundland"),
            Pair("Brasilia", "(GMT-03:00) Brasilia"),
            Pair("Buenos Aires", "(GMT-03:00) Buenos Aires"),
            Pair("Greenland", "(GMT-03:00) Greenland"),
            Pair("Montevideo", "(GMT-03:00) Montevideo"),
            Pair("Mid-Atlantic", "(GMT-02:00) Mid-Atlantic"),
            Pair("Azores", "(GMT-01:00) Azores"),
            Pair("Cape Verde Is.", "(GMT-01:00) Cape Verde Is."),
            Pair("Casablanca", "(GMT+00:00) Casablanca"),
            Pair("Dublin", "(GMT+00:00) Dublin"),
            Pair("Edinburgh", "(GMT+00:00) Edinburgh"),
            Pair("Lisbon", "(GMT+00:00) Lisbon"),
            Pair("London", "(GMT+00:00) London"),
            Pair("Monrovia", "(GMT+00:00) Monrovia"),
            Pair("UTC", "(GMT+00:00) UTC"),
            Pair("Amsterdam", "(GMT+01:00) Amsterdam"),
            Pair("Belgrade", "(GMT+01:00) Belgrade"),
            Pair("Berlin", "(GMT+01:00) Berlin"),
            Pair("Bern", "(GMT+01:00) Bern"),
            Pair("Bratislava", "(GMT+01:00) Bratislava"),
            Pair("Brussels", "(GMT+01:00) Brussels"),
            Pair("Budapest", "(GMT+01:00) Budapest"),
            Pair("Copenhagen", "(GMT+01:00) Copenhagen"),
            Pair("Ljubljana", "(GMT+01:00) Ljubljana"),
            Pair("Madrid", "(GMT+01:00) Madrid"),
            Pair("Paris", "(GMT+01:00) Paris"),
            Pair("Prague", "(GMT+01:00) Prague"),
            Pair("Rome", "(GMT+01:00) Rome"),
            Pair("Sarajevo", "(GMT+01:00) Sarajevo"),
            Pair("Skopje", "(GMT+01:00) Skopje"),
            Pair("Stockholm", "(GMT+01:00) Stockholm"),
            Pair("Vienna", "(GMT+01:00) Vienna"),
            Pair("Warsaw", "(GMT+01:00) Warsaw"),
            Pair("West Central Africa", "(GMT+01:00) West Central Africa"),
            Pair("Zagreb", "(GMT+01:00) Zagreb"),
            Pair("Athens", "(GMT+02:00) Athens"),
            Pair("Bucharest", "(GMT+02:00) Bucharest"),
            Pair("Cairo", "(GMT+02:00) Cairo"),
            Pair("Harare", "(GMT+02:00) Harare"),
            Pair("Helsinki", "(GMT+02:00) Helsinki"),
            Pair("Istanbul", "(GMT+02:00) Istanbul"),
            Pair("Jerusalem", "(GMT+02:00) Jerusalem"),
            Pair("Kaliningrad", "(GMT+02:00) Kaliningrad"),
            Pair("Kyiv", "(GMT+02:00) Kyiv"),
            Pair("Pretoria", "(GMT+02:00) Pretoria"),
            Pair("Riga", "(GMT+02:00) Riga"),
            Pair("Sofia", "(GMT+02:00) Sofia"),
            Pair("Tallinn", "(GMT+02:00) Tallinn"),
            Pair("Vilnius", "(GMT+02:00) Vilnius"),
            Pair("Baghdad", "(GMT+03:00) Baghdad"),
            Pair("Kuwait", "(GMT+03:00) Kuwait"),
            Pair("Minsk", "(GMT+03:00) Minsk"),
            Pair("Moscow", "(GMT+03:00) Moscow"),
            Pair("Nairobi", "(GMT+03:00) Nairobi"),
            Pair("Riyadh", "(GMT+03:00) Riyadh"),
            Pair("St. Petersburg", "(GMT+03:00) St. Petersburg"),
            Pair("Volgograd", "(GMT+03:00) Volgograd"),
            Pair("Tehran", "(GMT+03:30) Tehran"),
            Pair("Abu Dhabi", "(GMT+04:00) Abu Dhabi"),
            Pair("Baku", "(GMT+04:00) Baku"),
            Pair("Muscat", "(GMT+04:00) Muscat"),
            Pair("Samara", "(GMT+04:00) Samara"),
            Pair("Tbilisi", "(GMT+04:00) Tbilisi"),
            Pair("Yerevan", "(GMT+04:00) Yerevan"),
            Pair("Kabul", "(GMT+04:30) Kabul"),
            Pair("Ekaterinburg", "(GMT+05:00) Ekaterinburg"),
            Pair("Islamabad", "(GMT+05:00) Islamabad"),
            Pair("Karachi", "(GMT+05:00) Karachi"),
            Pair("Tashkent", "(GMT+05:00) Tashkent"),
            Pair("Chennai", "(GMT+05:30) Chennai"),
            Pair("Kolkata", "(GMT+05:30) Kolkata"),
            Pair("Mumbai", "(GMT+05:30) Mumbai"),
            Pair("New Delhi", "(GMT+05:30) New Delhi"),
            Pair("Sri Jayawardenepura", "(GMT+05:30) Sri Jayawardenepura"),
            Pair("Kathmandu", "(GMT+05:45) Kathmandu"),
            Pair("Almaty", "(GMT+06:00) Almaty"),
            Pair("Astana", "(GMT+06:00) Astana"),
            Pair("Dhaka", "(GMT+06:00) Dhaka"),
            Pair("Novosibirsk", "(GMT+06:00) Novosibirsk"),
            Pair("Urumqi", "(GMT+06:00) Urumqi"),
            Pair("Rangoon", "(GMT+06:30) Rangoon"),
            Pair("Bangkok", "(GMT+07:00) Bangkok"),
            Pair("Hanoi", "(GMT+07:00) Hanoi"),
            Pair("Jakarta", "(GMT+07:00) Jakarta"),
            Pair("Krasnoyarsk", "(GMT+07:00) Krasnoyarsk"),
            Pair("Beijing", "(GMT+08:00) Beijing"),
            Pair("Chongqing", "(GMT+08:00) Chongqing"),
            Pair("Hong Kong", "(GMT+08:00) Hong Kong"),
            Pair("Irkutsk", "(GMT+08:00) Irkutsk"),
            Pair("Kuala Lumpur", "(GMT+08:00) Kuala Lumpur"),
            Pair("Perth", "(GMT+08:00) Perth"),
            Pair("Singapore", "(GMT+08:00) Singapore"),
            Pair("Taipei", "(GMT+08:00) Taipei"),
            Pair("Ulaanbaatar", "(GMT+08:00) Ulaanbaatar"),
            Pair("Osaka", "(GMT+09:00) Osaka"),
            Pair("Sapporo", "(GMT+09:00) Sapporo"),
            Pair("Seoul", "(GMT+09:00) Seoul"),
            Pair("Tokyo", "(GMT+09:00) Tokyo"),
            Pair("Yakutsk", "(GMT+09:00) Yakutsk"),
            Pair("Adelaide", "(GMT+09:30) Adelaide"),
            Pair("Darwin", "(GMT+09:30) Darwin"),
            Pair("Brisbane", "(GMT+10:00) Brisbane"),
            Pair("Canberra", "(GMT+10:00) Canberra"),
            Pair("Guam", "(GMT+10:00) Guam"),
            Pair("Hobart", "(GMT+10:00) Hobart"),
            Pair("Melbourne", "(GMT+10:00) Melbourne"),
            Pair("Port Moresby", "(GMT+10:00) Port Moresby"),
            Pair("Sydney", "(GMT+10:00) Sydney"),
            Pair("Vladivostok", "(GMT+10:00) Vladivostok"),
            Pair("Magadan", "(GMT+11:00) Magadan"),
            Pair("New Caledonia", "(GMT+11:00) New Caledonia"),
            Pair("Solomon Is.", "(GMT+11:00) Solomon Is."),
            Pair("Srednekolymsk", "(GMT+11:00) Srednekolymsk"),
            Pair("Auckland", "(GMT+12:00) Auckland"),
            Pair("Fiji", "(GMT+12:00) Fiji"),
            Pair("Kamchatka", "(GMT+12:00) Kamchatka"),
            Pair("Marshall Is.", "(GMT+12:00) Marshall Is."),
            Pair("Wellington", "(GMT+12:00) Wellington"),
            Pair("Chatham Is.", "(GMT+12:45) Chatham Is."),
            Pair("Nuku'alofa", "(GMT+13:00) Nuku'alofa"),
            Pair("Tokelau Is.", "(GMT+13:00) Tokelau Is.")
        )

        z.forEach {
            zones.add(
                SelectorItem(
                    "",
                    it.second,
                    it.first,
                    icon = null,
                    color = 0,
                    isSelected = false,
                    isHeader = false,
                    isDisabled = false
                )
            )
        }

        return zones
    }

    fun countries(countries: List<Country>) {
        this.countries = countries
    }

    fun countries(): List<Country> {
        return countries
    }

    fun restaurantCountry(): Country {
        return countries.firstOrNull { it.code == restaurant()?.countryCode } ?: countries.first { it.code == "AE" }
    }

    fun countriesSelectorItems(country: Country?): MutableList<SelectorItem> {

        val countries: MutableList<SelectorItem> = mutableListOf()

        this.countries.forEach {
            countries.add(
                SelectorItem(
                    "", String.format("%s %s", it.phoneCode, it.name),
                    it,
                    icon = null,
                    color = 0,
                    isSelected = it.code == country?.code,
                    isHeader = false,
                    isDisabled = false
                )
            )
        }

        return countries.distinct().toMutableList()
    }

    fun productMessage(): Message? {
        return restaurant()?.productMessages?.minByOrNull { it.priority }
    }

    fun accountState(): AccountStateType {
        return when (featureFlagsManager.badgerEnabled) {
            true -> restaurant()?.accountState ?: AccountStateType.ACTIVE
            false -> AccountStateType.ACTIVE
        }
    }

    fun permission(restaurantId: String = restaurantId(), identifier: String): Permission {
        return user[restaurantId]?.permissions?.firstOrNull { it.name == identifier } ?: Permission()
    }

//    fun permissionColor(identifier: String): Int {
//
//        val permission = permission(identifier)
//
//        if (permission?.optionValue == fieldOptionError) {
//            return R.color.colorRed10
//        } else if (permission?.optionValue == fieldOptionWarning) {
//            return R.color.colorOrange10
//        }
//
//        return R.color.colorOrange10
//    }

//    fun permissionSolidColor(identifier: String): Int {
//
//        val permission = permission(identifier)
//
//        if (permission?.optionValue == fieldOptionError) {
//            return R.color.colorRed
//        } else if (permission?.optionValue == fieldOptionWarning) {
//            return R.color.colorOrange
//        }
//
//        return R.color.colorOrange
//    }

    fun sortReservations(reservations: List<Reservation>): List<Reservation> {
        return reservations.sortedBy {
            when (reservationsConfig.sortBy) {
                Configuration.RESERVATION_TIME -> it.startTime
                Configuration.CREATED_TIME -> it.createdAt
                Configuration.COVERS -> it.covers
                Configuration.NAME -> it.guestName.lowercase(Locale.ROOT)
                else -> it.startTime
            } as Comparable<Any>
        }
    }

    private fun lifecycleCategories(closedSections: List<String>): List<ReservationGroupSection> {
        /*
         Exclude pre_service from the list
         */
        val categories = Status.categories.map { it.clone() }.filter { it.code != Status.CategoriesLifeCycle.PRE_SERVICE.code }

        /*
         Add all pre_service statuses to upcoming category
         */
        categories.firstOrNull { it.code == Status.CategoriesLifeCycle.UPCOMING.code }?.statuses?.addAll(
            Status.categories.find { it.code == Status.CategoriesLifeCycle.PRE_SERVICE.code }?.statuses ?: listOf()
        )

        return categories.mapIndexed { index, model ->
            ReservationGroupSection(
                model.name,
                model.statuses,
                !closedSections.contains(model.name),
                mutableListOf(),
                fakeId = "$index"
            )
        }
    }

    fun groupReservations(
        reservations: List<Reservation>,
        date: Date?,
        configuration: Configuration,
        closedGroups: List<ReservationGroupSection>,
        closedSections: List<ReservationSection>
    ): List<ReservationGroupSection> {

        when (configuration) {

            Configuration.SHIFT -> {

                val shifts = shiftManager.createShiftsGroups(restaurantId(), reservations, date)

                val lifecycleCategories = lifecycleCategories(closedGroups.map { it.title })

                lifecycleCategories.forEach { category ->
                    shifts.forEach { shift ->
                        val filteredReservations = shift.reservations?.filter { res ->
                            category.statuses.map { it.code }.contains(res.status)
                        }

                        if (!filteredReservations.isNullOrEmpty()) {
                            category.items.add(
                                ReservationSection(
                                    shift.name,
                                    shift.color,
                                    filteredReservations,
                                    groupName = category.title,
                                    groupId = category.fakeId,
                                    fakeId = shift.fakeId
                                ).apply {
                                    expanded = sectionExpanded(closedSections, this)
                                }
                            )
                        }
                    }
                }

                return lifecycleCategories.filter { it.items.any { g -> g.items.isNotEmpty() } }
            }

            Configuration.STATUS -> {

                val statuses = mutableMapOf<String, MutableList<Reservation>>()

                val lifecycleCategories = lifecycleCategories(closedGroups.map { it.title })

                val categoriesOrderMap = Status.CategoriesLifeCycle.entries.associate { it.code to it.ordinal }

                val sortedCategories = Status.categories.sortedBy { categoriesOrderMap[it.code] ?: Int.MAX_VALUE}

                val statusSortingMap = sortedCategories
                    .flatMap { it.statuses } // Flatten the list of statuses
                    .map { it.code } // Extract the code of each status
                    .withIndex() // Pair each code with its position
                    .associate { it.value to it.index } // Create a map where key = code and value = position

                val sortedReservations = reservations.sortedBy { statusSortingMap[it.status] }

                sortedReservations.forEach {
                    if (statuses[it.status] == null) {
                        statuses[it.status] = mutableListOf()
                    }
                    statuses[it.status]?.add(it)
                }

                lifecycleCategories.forEach { category ->
                    statuses.forEach { mapEntry ->
                        val filteredReservations = mapEntry.value.filter { res ->
                            category.statuses.map { it.code }.contains(res.status)
                        }

                        if (filteredReservations.isNotEmpty()) {

                            category.items.add(
                                ReservationSection(
                                    Status.getTitle(mapEntry.key),
                                    Status.statusForCode(mapEntry.key)?.color ?: "",
                                    filteredReservations,
                                    groupName = category.title,
                                    groupId = category.fakeId,
                                    fakeId = ""
                                ).apply {
                                    expanded = sectionExpanded(closedSections, this)
                                }
                            )
                        }
                    }
                }

                return lifecycleCategories.filter { it.items.any { g -> g.items.isNotEmpty() } }
            }

            Configuration.LIFECYCLE -> {

                val lifecycleCategories = lifecycleCategories(closedGroups.map { it.title })

                lifecycleCategories.forEach { category ->
                    val filteredReservations =
                        reservations.filter {res ->
                            category.statuses.map { it.code }.contains(res.status)
                        }

                    if (filteredReservations.isNotEmpty()) {
                        category.items.add(
                            ReservationSection(
                                "",
                                "",
                                filteredReservations,
                                groupName = category.title,
                                groupId = category.fakeId,
                                fakeId = ""
                            ).apply {
                                expanded = sectionExpanded(closedSections, this)
                            }
                        )
                    }
                }

                return lifecycleCategories.filter { it.items.any { g -> g.items.isNotEmpty() } }
            }

            Configuration.BOTH -> {
                val shifts = shiftManager.createShiftsGroups(restaurantId(), reservations, date)

                val groups = shifts.map { shift ->

                    val statuses = mutableMapOf<String, MutableList<Reservation>>()

                    shift.reservations?.forEach {
                        if (statuses[it.status] == null) {
                            statuses[it.status] = mutableListOf()
                        }
                        statuses[it.status]?.add(it)
                    }

                    val sectionList = statuses.map { mapEntry ->
                        ReservationSection(
                            Status.getTitle(mapEntry.key),
                            Status.statusForCode(mapEntry.key)?.color ?: "",
                            mapEntry.value,
                            groupName = shift.name,
                            groupId = shift.fakeId,
                            fakeId = ""
                        ).apply {
                            expanded = sectionExpanded(closedSections, this)
                        }
                    }

                    val expanded = !closedGroups.any {
                        it.title == shift.name && it.fakeId == shift.fakeId
                    }

                    ReservationGroupSection(
                        title = shift.name,
                        listOf(),
                        expanded,
                        sectionList.toMutableList(),
                        fakeId = shift.fakeId
                    )
                }

                return groups.filter { it.items.any { it.items.isNotEmpty() } }
            }

            else -> {}
        }

        return listOf()
    }

    fun shiftForDay(date: Date) : List<Shift> {
        return shiftManager.shiftsForTheDay(restaurantId(), date).toList()
    }

    fun shiftForDayWithReservations(reservations: List<Reservation>, date: Date?) : List<Shift> {
        return shiftManager.createShiftsGroups(restaurantId(), reservations, date).toList()
    }

    fun groupPosRecords(posRecords: List<PosRecord?>?): List<PosSection> {

        return createPosLifecycleGroups(posRecords).map {
            PosSection(
                it.first.hashCode(),
                it.first,
                it.second
            )
        }
    }

    private fun sectionExpanded(
        closedSections: List<ReservationSection>,
        section: ReservationSection
    ): Boolean {
        return !closedSections.any { it.fakeId == section.fakeId && it.title == section.title && it.groupName == section.groupName }
    }
}