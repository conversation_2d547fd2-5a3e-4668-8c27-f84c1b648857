package com.eatapp.clementine.internal.managers

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.user.PermissionName
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.addTimeInterval
import com.eatapp.clementine.internal.endOfTheEatDay
import com.eatapp.clementine.internal.isBetween
import com.eatapp.clementine.internal.startOfTheEatDay
import com.eatapp.clementine.ui.common.tables.conflict.ConflictItem
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ConflictsManager @Inject constructor(
    val eatManager: EatManager,
    val shiftManager: ShiftManager
) {

    private val _conflictList = MutableLiveData<MutableList<ConflictItem>>()
    val conflictList: LiveData<MutableList<ConflictItem>> = _conflictList

    fun checkForConflicts(
        restaurantId: String,
        selectedTables: List<Table>,
        reservation: Reservation,
        reservations: List<Reservation>?,
        closings: List<Closing>
    ) {
        val conflictList = mutableListOf<ConflictItem>()

        val tables = selectedTables.toMutableList()

        val startTime =
            if (reservation.status == Status.Value.WAITLIST.code) reservation.waitlistQueuedAt else reservation.startTime
        val endTime = reservation.startTime.addTimeInterval(
            type = java.util.Calendar.SECOND, duration = reservation.duration
        )

        val conflictItemReservationOverlapping = reservationOverlapping(
            tables = tables,
            reservation = reservation,
            startTime = startTime,
            endTime = endTime,
            reservations = reservations
        )

        if (conflictItemReservationOverlapping.hasConflict) {
            conflictList.add(conflictItemReservationOverlapping)
        } else {
            conflictList.remove(conflictItemReservationOverlapping)
        }

        val conflictItemTableBlocked = isTableBlocked(
            reservation.startTime, tables = tables, closings = closings
        )
        if (conflictItemTableBlocked.hasConflict) {
            conflictItemTableBlocked.permission?.errorMessage = "Table blocked"
            conflictList.add(conflictItemTableBlocked)
        } else {
            conflictList.remove(conflictItemTableBlocked)
        }

        val conflictItemTableSelected = isTableSelected(restaurantId, reservation, tables)
        if (conflictItemTableSelected.hasConflict) {
            conflictList.add(conflictItemTableSelected)
        } else {
            conflictList.remove(conflictItemTableSelected)
        }

        val conflictItemCoversCapacity = tableCoversCapacityConflict(restaurantId, tables, reservation.covers)
        if (conflictItemCoversCapacity.hasConflict) {
            conflictList.add(conflictItemCoversCapacity)
        } else {
            conflictList.remove(conflictItemCoversCapacity)
        }

        val timeSlotBlocked = blockedTimeslotsConflict(restaurantId, startTime, closings)
        if (timeSlotBlocked.hasConflict) {
            conflictList.add(timeSlotBlocked)
        } else {
            conflictList.remove(timeSlotBlocked)
        }

        _conflictList.value = conflictList
    }

    fun removeConflict(conflictItem: ConflictItem) {
        _conflictList.value?.let {
            it.remove(conflictItem)
            _conflictList.postValue(it)
        }
    }

    private fun reservationOverlapping(
        tables: MutableList<Table>?,
        reservation: Reservation?,
        startTime: Date?,
        endTime: Date?,
        reservations: List<Reservation>?
    ): ConflictItem {

        val permission = eatManager.permission(identifier = PermissionName.RESERVATION_TIME.permissionName).copy()

        val conflictItem = ConflictItem()

        if (permission.value == fieldOptionHide) {
            return conflictItem
        }

        if (tables?.isEmpty() == true) {
            return conflictItem
        }

        if (reservation == null || startTime == null || endTime == null) {
            return conflictItem
        }

        var isOverlapping = false

        val reservationsOnTables = arrayListOf<Reservation>()

        for (table in tables ?: arrayListOf()) {
            reservations?.forEach {
                if (it.tables?.contains(table) == true) {
                    reservationsOnTables.add(it)
                }
            }
        }

        for (rsvr in reservationsOnTables) {
            if (!isOverlapping && rsvr.id != reservation.id) {
                isOverlapping = this.isOverlapping(
                    startTime,
                    endTime,
                    reservation.status,
                    rsvr
                )
            }
        }

        conflictItem.hasConflict = isOverlapping
        conflictItem.permission = if (isOverlapping) permission else null

        return conflictItem
    }

    private fun isOverlapping(
        startDate: Date,
        endDate: Date,
        status: String,
        res: Reservation
    ): Boolean {

        val isSameStartDate = startDate == res.startTime
        val isSameEndDate = endDate == res.endTime
        val isInner = startDate > res.startTime && endDate < res.endTime
        val isOuter = startDate < res.startTime && endDate > res.endTime
        val isConflicting =
            res.endTime.isBetween(startDate, endDate) || res.startTime.isBetween(
                startDate,
                endDate
            )

        val firstResNormalStatus = !Status.isRemoved(status)
        val secondResNormalStatus = !Status.isRemoved(res.status)

        return (isSameStartDate || isSameEndDate || isInner || isOuter || isConflicting) && (firstResNormalStatus && secondResNormalStatus)
    }

    private fun isTableBlocked(
        reservationTime: Date?,
        tables: MutableList<Table>?,
        closings: List<Closing>
    ): ConflictItem {

        val permission = eatManager.permission(identifier = PermissionName.OVERRIDE_BLOCKED_TIMESLOTS_CONFLICT.permissionName).copy()
        val conflictItem = ConflictItem()

        if (permission.value == fieldOptionHide) {
            return conflictItem
        }

        val blocked = isTableBlockedFor(reservationTime, tables, closings)

        conflictItem.permission = if (blocked) permission else null
        conflictItem.hasConflict = blocked

        return conflictItem
    }

    private fun isTableBlockedFor(
        reservationTime: Date?,
        tables: MutableList<Table>?,
        closings: List<Closing>
    ): Boolean {

        if (reservationTime == null) {
            return false
        }

        var blocked = false

        // Only take into account closings that are relevant for reservation start time
        val filteredClosings = closings.filter { reservationTime.isBetween(it.timeRangeBegin, it.timeRangeEnd) }

        for (closing in filteredClosings) {
            if (!closing.closingTypes.contains("in_house")) {
                continue
            }

            val closingTableIds = closing.tableIds

            val tableIdStrings = tables?.map { it.id }
            val set1 = closingTableIds.toHashSet()
            val set2 = tableIdStrings?.toHashSet() ?: emptySet()

            if (set1.intersect(set2).isNotEmpty()) {
                blocked = true
                break
            }
        }

        return blocked
    }

    private fun isTableSelected(
        restaurantId: String,
        reservation: Reservation,
        tables: MutableList<Table>?
    ): ConflictItem {

        val tableSelected = !tables.isNullOrEmpty()

        val permission = eatManager.permission(identifier = PermissionName.MANDATORY_TABLE_SELECTION.permissionName).copy()
        val conflictItem = ConflictItem()

        if (reservation.status == Status.Value.WAITLIST.code) {
            return conflictItem
        }

        val shift = shiftManager.shift(restaurantId = restaurantId, reservation = reservation)

        if (shift?.availabilityType == "tables") {
            permission.value = fieldOptionError
            conflictItem.permission = if (!tableSelected) permission else null
            conflictItem.hasConflict = !tableSelected
            return conflictItem
        }

        if (permission.value == fieldOptionHide) {
            return conflictItem
        }

        conflictItem.permission = if (!tableSelected) permission else null
        conflictItem.hasConflict = !tableSelected

        return conflictItem
    }

    private fun tableCoversCapacityConflict(
        restaurantId: String,
        tables: MutableList<Table>?,
        covers: Int
    ): ConflictItem {

        val permission = eatManager.permission(restaurantId, PermissionName.RESERVATION_TABLE_COVERS.permissionName).copy()
        val conflictItem = ConflictItem()

        if (permission.value == fieldOptionHide) {
            return conflictItem
        }

        if (tables == null) {
            return conflictItem
        }

        var maxNumberOfCovers = 0
        var minNumberOfCovers = 0

        for (table in tables) {
            maxNumberOfCovers += table.maxCovers
            minNumberOfCovers += table.minCovers
        }

        val underUtilised = (minNumberOfCovers != 0 && covers < minNumberOfCovers)
        val overUtilised = (maxNumberOfCovers != 0 && covers > maxNumberOfCovers)

        val coversOutOfRange = underUtilised || overUtilised

        if (coversOutOfRange) {

            val limitDescription = if (underUtilised) minNumberOfCovers else maxNumberOfCovers
            val limitWord = if (underUtilised) "minimum" else "maximum"
            val sizeWord = if (underUtilised) "smaller" else "bigger"

            if (tables.size == 1) {
                permission.errorMessage =
                    "The table has a $limitWord limit of $limitDescription people. You may need to change the table to a $sizeWord one."
            } else {
                permission.errorMessage =
                    "These tables have a $limitWord limit of $limitDescription people. You may need to change the tables to a $sizeWord ones."
            }
        }

        conflictItem.permission = if (coversOutOfRange) permission else null
        conflictItem.hasConflict = coversOutOfRange

        return conflictItem
    }

    private fun blockedTimeslotsConflict(
        restaurantId: String,
        selectedDate: Date?,
        closings: List<Closing>?
    ): ConflictItem {

        val permission = eatManager.permission(restaurantId, PermissionName.OVERRIDE_BLOCKED_TIMESLOTS_CONFLICT.permissionName).copy()
        val conflictItem = ConflictItem()

        if (permission.value == fieldOptionHide) {
            return conflictItem
        }

        if (selectedDate == null) {
            return conflictItem
        }

        val todaysClosings = closings?.filter {
            it.timeRangeBegin.isBetween(
                startOfTheEatDay(Date()),
                endOfTheEatDay(date = Date())
            )
        }

        var blocked = false

        // check blocked hours
        todaysClosings?.forEach {
            if (it.tableIds.isEmpty()) {
                if (it.timeRangeBegin == selectedDate || selectedDate.isBetween(
                        it.timeRangeBegin,
                        it.timeRangeEnd
                    )
                ) {
                    blocked = true
                }
            }
        }

        conflictItem.hasConflict = blocked
        conflictItem.permission = if (blocked) permission else null
        conflictItem.timeBlocked = true

        return conflictItem
    }
}