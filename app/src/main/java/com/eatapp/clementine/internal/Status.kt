package com.eatapp.clementine.internal

import android.annotation.SuppressLint
import android.content.res.Resources
import android.graphics.Color
import android.text.format.DateUtils
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.restaurant.ReservationStatus
import com.eatapp.clementine.data.network.response.restaurant.ReservationStatusCategory
import com.eatapp.clementine.data.network.response.restaurant.UseStatus
import java.util.Date
import androidx.core.graphics.toColorInt

open class Status {

    enum class CategoriesLifeCycle(val code: String) {
        PRE_SERVICE("pre_service"),
        UPCOMING("upcoming"),
        IN_SERVICE("in_service"),
        REMOVED("removed")
    }

    enum class Value(val code: String) {
        CONFIRMED("confirmed"),
        DENIED("denied"),
        FINISHED("finished"),
        NOT_CONFIRMED("not_confirmed"),
        WAITLIST("wait_list");
    }

    companion object {

        var categories: List<ReservationStatusCategory> = mutableListOf()

        var resources: Resources? = null
        var packageName: String? = null

        fun statusForCode(status: String): ReservationStatus? {
            categories.flatMap { it.statuses }.forEach { s ->
                if (s.code == status) return s
            }
            return null
        }

        fun categoryForLifeCycle(code: CategoriesLifeCycle): ReservationStatusCategory? {
            return categories.firstOrNull { it.code == code.code }
        }

        private fun categoryForStatus(status: String): ReservationStatusCategory? {
            return categories.firstOrNull { it.statuses.find { s -> s.code == status } != null }
        }

        fun isSeated(status: String): Boolean {
            return categoryForStatus(status)?.code == CategoriesLifeCycle.IN_SERVICE.code
        }

        fun isUpcomingUi(status: String): Boolean {
            return categoryForStatus(status)?.code == CategoriesLifeCycle.UPCOMING.code ||
                    categoryForStatus(status)?.code == CategoriesLifeCycle.PRE_SERVICE.code
        }

        fun isUpcoming(status: String): Boolean {
            return categoryForStatus(status)?.code == CategoriesLifeCycle.UPCOMING.code
        }

        fun isPreService(status: String): Boolean {
            return categoryForStatus(status)?.code == CategoriesLifeCycle.PRE_SERVICE.code
        }

        fun isRemoved(status: String): Boolean {
            return categoryForStatus(status)?.code == CategoriesLifeCycle.REMOVED.code
        }

        fun isFinished(status: String): Boolean {
            return status == Value.FINISHED.code
        }

        fun getTitle(status: String): String {
            categories.flatMap { it.statuses }.forEach { s ->
                if (s.code == status) return s.name
            }
            return ""
        }

        @SuppressLint("DiscouragedApi")
        fun getIcon(status: String?): Int {
            categories.flatMap { it.statuses }.forEach { s ->
                if (s.code == status) {
                    return resources?.getIdentifier("ic_icon_status_${s.icon}", "drawable",
                        packageName) ?: R.drawable.ic_icon_status_not_confirmed
                }
            }
            return R.drawable.ic_icon_status_not_confirmed
        }

        @SuppressLint("DiscouragedApi")
        fun getTableIcon(status: String?): Int {
            categories.flatMap { it.statuses }.forEach { s ->
                if (s.code == status) {
                    return resources?.getIdentifier("ic_icon_${s.icon}", "drawable",
                        packageName) ?: R.drawable.ic_icon_not_confirmed
                }
            }
            return R.drawable.ic_icon_not_confirmed
        }

        fun getColor(status: String?): Int {
            categories.flatMap { it.statuses }.forEach { s ->
                if (s.code == status) return s.color.toColorInt()
            }
            return R.color.colorPrimary
        }

        private fun shouldShowStatus(code: String, isToday: Boolean, isInPast: Boolean): Boolean {
            val status = statusForCode(code)

            status?.let {
                if ((isToday && status.forReservationDay)
                    || (!status.forPastReservation && !status.forReservationDay)) { //today
                    return true
                } else if (!isInPast && status.forReservationDay) { //future
                    return true
                } else if (isInPast && status.forPastReservation) { //past
                    return true
                }
            }

            return false
        }

        @SuppressLint("DiscouragedApi")
        fun statusesList(
            selectedStatus: String?,
            isFreemium: Boolean,
            date: Date
        ): MutableList<SelectorItem> {

            val statuses = mutableListOf<SelectorItem>()
            val isToday = DateUtils.isToday(date.time)
            val isPast = date.before(Date())

            val initial = statusForCode(selectedStatus ?: "")

            val suggestedStatuses = initial?.nextStatuses?.mapNotNull {
                statusForCode(it.code)
            }?.filter {
                it.useStatus == UseStatus.ACTIVE
                        && shouldShowStatus(it.code, isToday, isPast)
            }?.sortedBy { it.position }

            if (suggestedStatuses?.isNotEmpty() == true) {

                statuses.add(SelectorItem(
                    "",
                    "Suggested",
                    null,
                    isSelected = false,
                    isHeader = true,
                    isDisabled = false
                ))

                suggestedStatuses.forEach {
                    val s = statusForCode(it.code)
                    statuses.add(
                        SelectorItem(
                            "",
                            s!!.name,
                            s.code,
                            resources?.getIdentifier(
                                "ic_icon_status_${s.icon}",
                                "drawable",
                                packageName
                            ),
                            Color.parseColor(s.color),
                            isSelected = selectedStatus == s.code,
                            isHeader = false,
                            isDisabled = isFreemium && !(s.freemium)
                        )
                    )
                }
            }

            categories.forEach {
                statuses.addAll(
                    statusGroup(
                        it.statuses.filter { s ->
                            (s.useStatus == UseStatus.ACTIVE
                                    && shouldShowStatus(s.code, isToday, isPast))
                                    || s.code == initial?.code }.sortedBy { it.position },
                        it.name,
                        isFreemium,
                        selectedStatus
                    )
                )
            }

            return statuses
        }

        @SuppressLint("DiscouragedApi")
        private fun statusGroup(
            group: List<ReservationStatus>,
            header: String,
            isFreemium: Boolean,
            selectedStatus: String?
        ): List<SelectorItem> {

            val statuses = mutableListOf<SelectorItem>()

            val st = SelectorItem(
                "",
                header,
                null,
                isSelected = false,
                isHeader = true,
                isDisabled = false
            )
            statuses.add(st)

            group.forEach { s ->
                val status = SelectorItem(
                    "",
                    s.name,
                    s.code,
                    resources?.getIdentifier("ic_icon_status_${s.icon}", "drawable", packageName),
                    Color.parseColor(s.color),
                    isSelected = selectedStatus == s.code,
                    isHeader = false,
                    isDisabled = isFreemium && !(s.freemium)
                )
                statuses.add(status)
            }

            return statuses
        }
    }
}