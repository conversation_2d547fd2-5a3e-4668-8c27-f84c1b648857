@file:Suppress("UNCHECKED_CAST")

package com.eatapp.clementine

import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.data.repository.GuestsRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.managers.EatManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Qualifier

sealed class VoucherUpdateState<out T> {
    data object Idle : VoucherUpdateState<Nothing>()
    data object Loading : VoucherUpdateState<Nothing>()
    data class Success<T>(val data: T) : VoucherUpdateState<T>()
    data class Error(val exception: EatException) : VoucherUpdateState<Nothing>()
}

class ConsumableEvent<out T>(private val content: T) {
    private var hasBeenHandled = false

    fun getContentIfNotHandled(): T? =
        if (hasBeenHandled) null
        else {
            hasBeenHandled = true
            content
        }

    fun peekContent(): T = content
}

interface VouchersHandler {
    suspend fun <T> redeemVoucherFlow(
        id: String,
        reservationId: String? = null,
        editedBy: String?,
        assignment: VoucherAssignment
    ): Flow<VoucherUpdateState<T>>

    fun <T> updateVoucherAssignmentsFlow(
        id: String,
        editedBy: String?,
        vouchers: List<Voucher>
    ): Flow<VoucherUpdateState<T>>
}

class GuestVouchersHandlerImpl @Inject constructor(
    private val guestsRepository: GuestsRepository
) : VouchersHandler {

    override fun <T> updateVoucherAssignmentsFlow(
        id: String,
        editedBy: String?,
        vouchers: List<Voucher>
    ): Flow<VoucherUpdateState<T>> {
        return executeSafeFlow {
            val addedVoucherAssignments = vouchers.map { it.id }
            guestsRepository.updateVouchersForGuest(
                id,
                AssignVouchersRequest(addedVoucherAssignments, editedBy = null)
            ).guest as T
        }
    }

    override suspend fun <T> redeemVoucherFlow(
        id: String,
        reservationId: String?,
        editedBy: String?,
        assignment: VoucherAssignment
    ): Flow<VoucherUpdateState<T>> {
        return executeSafeFlow {
            val body = RedeemVoucherAssignmentRequest(
                RedeemVoucherAssignmentRequest.RedeemVoucherAssignmentBody(
                    assignment.id,
                    assignment.attributes.code,
                    reservationId,
                    null
                )
            )

            guestsRepository.redeemVoucherForGuest(
                id,
                body
            ).guest as T
        }
    }
}

class ReservationVouchersHandlerImpl @Inject constructor(
    private val reservationsRepository: ReservationsRepository,
    private val eatManager: EatManager
) : VouchersHandler {
    override fun <T> updateVoucherAssignmentsFlow(
        id: String,
        editedBy: String?,
        vouchers: List<Voucher>
    ): Flow<VoucherUpdateState<T>> {
        return executeSafeFlow {
            val addedVoucherAssignments = vouchers.map { it.id }
            reservationsRepository.updateVouchersForReservation(
                eatManager.restaurantId(),
                id,
                AssignVouchersRequest(addedVoucherAssignments, editedBy = editedBy)
            ).reservation as T
        }
    }

    override suspend fun <T> redeemVoucherFlow(
        id: String,
        reservationId: String?,
        editedBy: String?,
        assignment: VoucherAssignment
    ): Flow<VoucherUpdateState<T>> {
        return executeSafeFlow {
            val body = RedeemVoucherAssignmentRequest(
                RedeemVoucherAssignmentRequest.RedeemVoucherAssignmentBody(
                    assignment.id,
                    assignment.attributes.code,
                    editedBy = editedBy
                )
            )

            reservationsRepository.redeemVoucherForReservation(
                eatManager.restaurantId(),
                id,
                body
            ).reservation as T
        }
    }
}

fun <T> executeSafeFlow(
    block: suspend () -> T
): Flow<VoucherUpdateState<T>> = flow {
    emit(VoucherUpdateState.Loading)

    try {
        val result = block()
        emit(VoucherUpdateState.Success(result))
    } catch (e: Exception) {
        val eatException = EatException(e, false)
        emit(VoucherUpdateState.Error(eatException))
    }
}

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GuestVouchersHandler

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ReservationVouchersHandler