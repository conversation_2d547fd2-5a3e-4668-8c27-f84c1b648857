package com.eatapp.clementine.enums

import com.eatapp.clementine.R

enum class SettingsEntry(var title: String?, val icon: Int, var arrow: <PERSON><PERSON><PERSON>) {

    RESTAURANT("Name", R.drawable.ic_icon_restaurant, true),
    GUEST_MANAGEMENT("Guest management", R.drawable.ic_icon_people, true),
    SUPPORT("Support", R.drawable.ic_icon_support, true),
    VOUCHER("Redeem Voucher", R.drawable.ic_icon_receipt, true),
    LOG_OUT("Log out", R.drawable.ic_icon_logout, false),
    PRINT_SETTINGS("Print settings", R.drawable.ic_icon_print, true),
    RESERVATION_VIEW_MODE("Reservation list view", R.drawable.ic_icon_cutlery, true),
    OMNISEARCH("Omnisearch", R.drawable.ic_icon_search_grey600, true);

    companion object {

        val list = arrayListOf(
            RESTAURANT,
            GUEST_MANAGEMENT,
            SUPPORT,
            VOUCHER,
            LOG_OUT,
            PRINT_SETTINGS,
            RESERVATION_VIEW_MODE,
            OM<PERSON>SEARCH
        )
    }
}
