package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.comment.Comment
import com.eatapp.clementine.databinding.ListItemCommentBinding
import com.eatapp.clementine.internal.tintColor


class CommentsAdapter(val listener: (Int) -> Unit) :
    ListAdapter<Comment, CommentsAdapter.ItemViewHolder>(CommentsItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(
            ListItemCommentBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(createOnClickListener(item, holder, position), item)
    }

    private fun createOnClickListener(
        comment: Comment,
        holder: ItemViewHolder,
        position: Int
    ): View.OnClickListener {
        return View.OnClickListener {
            if (comment.isInEditMode) {
                comment.comment = holder.itemView.findViewById<TextView>(R.id.com).text.toString()
            }

            comment.isInEditMode = !comment.isInEditMode
            listener(position)
        }
    }

    class ItemViewHolder(
        private val binding: ListItemCommentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, i: Comment) {
            binding.apply {
                editClickListener = l
                comment = i
                executePendingBindings()
            }

            if (i.isInEditMode) {
                binding.com.requestFocus()
                binding.editIcon.tintColor(R.color.colorPrimary)
            } else {
                binding.editIcon.tintColor(R.color.colorDark50)
            }
        }
    }
}

private class CommentsItemDiffCallback : DiffUtil.ItemCallback<Comment>() {

    override fun areItemsTheSame(oldItem: Comment, newItem: Comment): Boolean {
        return oldItem.comment == newItem.comment
    }

    override fun areContentsTheSame(oldItem: Comment, newItem: Comment): Boolean {
        return oldItem == newItem
    }
}