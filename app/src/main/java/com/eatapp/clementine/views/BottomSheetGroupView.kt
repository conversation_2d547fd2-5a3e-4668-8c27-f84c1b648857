package com.eatapp.clementine.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.ItemsExpandableAdapter
import com.eatapp.clementine.databinding.BottomSheetLayoutBinding
import com.eatapp.clementine.internal.ExpandableItemAnimator
import com.eatapp.clementine.internal.ItemsGroup
import kotlin.math.roundToInt

@SuppressLint("ViewConstructor")
class BottomSheetGroupView(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    items: MutableList<ItemsGroup>,
    actionTitle: String,
    singleSelection: Boolean
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var dismiss: (() -> Unit)? = null

    private val binding: BottomSheetLayoutBinding =
        BottomSheetLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    init {

        val adapters: List<ItemsExpandableAdapter> = items.map { itemsGroup ->
            val adapter = ItemsExpandableAdapter(itemsGroup, singleSelection)
            adapter.dismiss = {
                dismiss?.let { it() }
            }
            return@map adapter
        }

        val mergeAdapterConfig = ConcatAdapter.Config.Builder()
            .setIsolateViewTypes(false)
            .build()
        val mergeAdapter = ConcatAdapter(mergeAdapterConfig, adapters)

        with(binding.list) {
            layoutManager = LinearLayoutManager(context)
            itemAnimator =
                ExpandableItemAnimator()
            adapter = mergeAdapter
        }

        if (singleSelection) {
            binding.actionCont.visibility = View.GONE
        } else {
            binding.applyBtn.setOnClickListener {
                dismiss?.let { it() }
            }
        }

        val displayMetrics = DisplayMetrics()
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val height = displayMetrics.heightPixels

        val maxHeight = (height * 0.80).roundToInt()
        val listSize = items.size * context.resources.getDimension(R.dimen.selector_item_height).toInt()

        val layoutParams = binding.list.layoutParams as LinearLayout.LayoutParams

        if (listSize > maxHeight) {
            layoutParams.height = maxHeight
        } else {
            layoutParams.height = listSize
        }

        binding.list.layoutParams = layoutParams

        binding.applyBtn.setTitle(actionTitle)
    }
}