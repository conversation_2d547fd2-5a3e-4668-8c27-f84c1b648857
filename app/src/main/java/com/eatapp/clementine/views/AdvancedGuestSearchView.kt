package com.eatapp.clementine.views

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Typeface
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.StyleSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.ViewGroup
import android.widget.EditText
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.GuestAdapter
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.databinding.AdvancedGuestSearchLayoutBinding
import com.eatapp.clementine.internal.getDrawable
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber

interface AdvancedGuestSearchListener {
    // Invoked with combined text from all input fields, when focus is changed
    fun onFocusChanged(query: String)

    // Invoked when guest is selected from list of results
    fun onGuestSelected(guest: Guest)

    // Invoked when there are no results from BE, and there is some kind of input, to enable easier guest creation
    fun onMoreDetailsClicked(guestDraft: Guest)

    // Invoked when user expands/collapses guest list
    fun onGuestListExpanded(expanded: Boolean)

    // Invoked after this view populates guest data if only one guest has been found and phone or email are present
    fun onAutoCompletePerformed()
}

class AdvancedGuestSearchView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val expandedInputPercentage = 0.5f
    private val defaultInputPercentage = 0.6f

    var listener: AdvancedGuestSearchListener? = null

    private val phoneNumberUtil = PhoneNumberUtil.getInstance()
    private val textWatcher = SearchTextWatcher()

    private val binding =
        AdvancedGuestSearchLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    private val adapter = GuestAdapter {
        guestSelected(it)
    }

    private val inputFields = listOf(
        binding.etFirstName,
        binding.etLastName,
        binding.etEmail,
        binding.etPhone,
    )

    private var country: Country? = null
    private var countries: List<Country>? = null

    private val focusChangeListener = OnFocusChangeListener { v, hasFocus ->
        if (v.id == binding.etPhone.id) {
            binding.ivFlagCont.visibleOrGone = !hasFocus
            if (!hasFocus) {
                val text = binding.etPhone.text
                setCountry(country(text.toString()))
                binding.etPhone.text = text

                if (text.isEmpty() || text.toString() == "+") {
                    setCountry(this.country)
                }
            }
        }

        updateGuideLinePosition()

        if (isInputEmpty()) {
            lastExecutedQuery = ""
            updateListVisibility(false)
            binding.containerAdditionalText.visibleOrGone = false
            return@OnFocusChangeListener
        }

        if (!hasFocus) {
            invokeListener()
        }

        updateButtonTitle(query.trim())
        updateButtonState()
    }

    private var lastExecutedQuery: String = ""

    val query: String
        get() {
            return inputFields
                .map { it.text.toString().trim() }
                .filter { it.isNotEmpty() && !it.isCountryPhoneCode() && it != "+" }
                .joinToString(separator = " ")
    }

    init {

        inputFields.forEach {
            it.onFocusChangeListener = focusChangeListener
        }

        inputFields.forEach {
            it.addTextChangedListener(textWatcher)
        }

        binding.rvGuests.adapter = adapter

        binding.containerAdditionalText.setOnClickListener {
            clearFocus()

            if (!binding.ivChevron.visible) {
                listener?.onMoreDetailsClicked(
                    draftGuest()
                )
                return@setOnClickListener
            }

            updateListVisibility(!binding.rvGuests.visible)
        }

        binding.btnSave.title.typeface = ResourcesCompat.getFont(context, R.font.inter_medium)
        binding.btnSave.title.maxLines = 1
        binding.btnSave.title.ellipsize = TextUtils.TruncateAt.MIDDLE
    }

    fun setOnFlagClickListener(listener: (View) -> Unit) {
        binding.ivFlagCont.setOnClickListener(listener)
    }

    fun setOnSaveGuestClickListener(function: (Guest) -> Unit) {
        binding.btnSave.setOnClickListener {
            function.invoke(
                draftGuest()
            )
        }
    }

    fun setCountry(country: Country?) {
        this.country = country

        if (country == null) {
            return
        }

        binding.run {
            if (etPhone.text.isEmpty() || etPhone.text.toString() == "+" || etPhone.text.toString().isCountryPhoneCode()) {
                etPhone.setText(country.phoneCode)
            }
            Glide.with(this@AdvancedGuestSearchView)
                .load(context.getDrawable("flag_${country.code.lowercase()}"))
                .fallback(context.getDrawable("flag_ae"))
                .transform(CenterCrop(), RoundedCorners(3.px))
                .into(ivFlag)
        }
    }

    fun currentCountry(): Country? {
        return country
    }

    fun setCountries(countries: List<Country>) {
        this.countries = countries
    }

    fun singleGuestFound(guest: Guest, allowAutoComplete: Boolean) {
        showProgress(false)

        val emailEmpty = binding.etEmail.text.isEmpty()
        val phoneEmpty = phoneText().isEmpty()

        if (emailEmpty && phoneEmpty) {
            multipleGuestFound(listOf(guest))
            return
        }

        if (!allowAutoComplete) {
            multipleGuestFound(listOf(guest))
            return
        }

        binding.run {
            containerAdditionalText.visibleOrGone = true
            tvAdditionalText.text = context.getString(R.string.add_more_details)
            ivChevron.visibleOrGone = false
            updateListVisibility(false)

            // Set data without triggering the watcher, to avoid triggering listener again
            inputFields.forEach {
                it.removeTextChangedListener(textWatcher)
            }
            etFirstName.setText(guest.firstName)
            etLastName.setText(guest.lastName)
            etEmail.setText(guest.email)
            etPhone.setText(guest.phone)
            inputFields.forEach {
                it.addTextChangedListener(textWatcher)
            }
            root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.green50))
            listener?.onAutoCompletePerformed()
        }
    }

    fun noGuestsFound() {
        showProgress(false)
        updateListVisibility(false)

        if (isInputEmpty()) {
            binding.containerAdditionalText.visibleOrGone = false
        } else {
            binding.containerAdditionalText.visibleOrGone = true
            binding.tvAdditionalText.text = context.getString(R.string.add_more_details)
        }

        adapter.submitList(emptyList())
        binding.ivChevron.visibleOrGone = false
    }

    fun multipleGuestFound(guests: List<Guest>) {
        showProgress(false)

        binding.run {
            val text = if (guests.size == 1) {
                context.getString(R.string.guest_found)
            } else {
                context.getString(R.string.guests_found)
            }
            containerAdditionalText.visibleOrGone = true
            binding.ivChevron.visibleOrGone = true
            tvAdditionalText.text = String.format(
                "%s %s", guests.size.toString(),
                text.lowercase()
            )
            adapter.submitList(
                guests,
                binding.etFirstName.text.toString(),
                binding.etLastName.text.toString(),
                binding.etPhone.text.toString()
            )
        }
    }

    fun showButtonLoading(show: Boolean) {
        binding.btnSave.showLoading(show)
    }

    fun isInputEmpty(): Boolean {
        return listOf(
            binding.etFirstName.text,
            binding.etLastName.text,
            binding.etEmail.text,
            phoneText()
        ).none { it.isNotEmpty() }
    }

    fun draftGuest(): Guest {
        return Guest(
            firstName = binding.etFirstName.text.toString(),
            lastName = binding.etLastName.text.toString(),
            email = binding.etEmail.text.toString(),
            phone = phoneText()
        )
    }

    fun guests(): List<Guest> {
        return adapter.currentList
    }

    fun updateListVisibility(visible: Boolean) {
        binding.rvGuests.visibleOrGone = visible
        binding.bottomLine.visibleOrGone = !visible
        binding.btnSave.visibleOrGone = visible
        binding.ivChevron.setImageResource(
            if (visible) {
                R.drawable.ic_icon_arrow_up
            } else {
                R.drawable.ic_icon_arrow_down
            }
        )

        if (visible) {
            updateButtonTitle(query.trim())
            updateButtonState()
        }

        listener?.onGuestListExpanded(visible)
    }

    fun showProgress(show: Boolean) {
        binding.progress.visibleOrGone = show
    }

    fun phoneText(): String {
        return binding.etPhone.text.toString().takeIf { it != "+" && !it.isCountryPhoneCode() } ?: ""
    }

    fun emailText(): String {
        return binding.etEmail.text.trim().toString()
    }

    private fun invokeListener() {
        if (query == lastExecutedQuery) {
            return
        }

        binding.root.setBackgroundColor(ContextCompat.getColor(context, R.color.grey50))

        if (query.isEmpty()) {
            binding.containerAdditionalText.visibleOrGone = false
            updateListVisibility(false)
        }

        showProgress(true)
        listener?.onFocusChanged(query)

        lastExecutedQuery = query
    }

    private fun guestSelected(guest: Guest) {
        binding.containerAdditionalText.visibleOrGone = false
        updateListVisibility(false)
        clearFocus()
        listener?.onGuestSelected(guest)
    }

    private fun updateGuideLinePosition() = with(binding) {
        if (etPhone.hasFocus()) {
            animateGuideline(defaultInputPercentage, binding.guidelineVerticalPhoneEmil)
        } else if (etEmail.hasFocus()) {
            animateGuideline(expandedInputPercentage, binding.guidelineVerticalPhoneEmil)
            animateGuideline(defaultInputPercentage, binding.guidelineVerticalNames)
        } else if (etFirstName.hasFocus()) {
            animateGuideline(defaultInputPercentage, binding.guidelineVerticalNames)
        } else if (etLastName.hasFocus()) {
            animateGuideline(expandedInputPercentage, binding.guidelineVerticalNames)
            animateGuideline(defaultInputPercentage, binding.guidelineVerticalPhoneEmil)
        }
    }

    private fun animateGuideline(targetPercent: Float, guideline: Guideline) {
        val layoutParams = guideline.layoutParams as ConstraintLayout.LayoutParams
        val startPercent = layoutParams.guidePercent

        if (startPercent == targetPercent) {
            return
        }

        ValueAnimator.ofFloat(startPercent, targetPercent).apply {
            duration = 300
            addUpdateListener { animator ->
                val newPercent = animator.animatedValue as Float
                layoutParams.guidePercent = newPercent
                guideline.layoutParams = layoutParams
            }
            start()
        }
    }

    private fun updateButtonTitle(query: String) {
        val fullText = "Add '$query' as a new guest"
        val spannable = SpannableString(fullText)
        val start = fullText.indexOf(query)
        if (start >= 0) {
            spannable.setSpan(
                StyleSpan(Typeface.BOLD),
                start,
                start + query.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.btnSave.title.text = spannable
    }

    private fun updateButtonState() {
        if (binding.etFirstName.text.isNullOrEmpty()) {
            binding.btnSave.state = LoadingButton.LoadingButtonState.Disabled
        } else {
            binding.btnSave.state = LoadingButton.LoadingButtonState.AvailableLight
        }
    }

    private fun String.isCountryPhoneCode(): Boolean {
        val countryCode = this.trim()
        return countries?.any { it.phoneCode == countryCode } == true
    }

    private fun country(phoneCode: String): Country? {
        val restaurantCountry = countries?.firstOrNull {
            it.code.equals(currentCountry()?.code, ignoreCase = true)
        }

        return if (phoneCode.isEmpty()) {
            restaurantCountry
        } else {
            try {
                val number = phoneNumberUtil.parse(phoneCode, null)
                countries?.firstOrNull { it.phoneCode.toInt() == number.countryCode }
            } catch (e: Exception) {
                countries?.firstOrNull { phoneCode.startsWith(it.phoneCode) } ?: restaurantCountry
            }
        }
    }

    private inner class SearchTextWatcher() : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable?) {
            if (binding.etPhone.hasFocus()) {
                    if (s.toString().isEmpty()) {
                        binding.etPhone.setText("+")
                        binding.etPhone.setSelection(binding.etPhone.text.length)
                    }

                    try {
                        if (phoneNumberUtil.isValidNumber(
                                phoneNumberUtil.parse(
                                    binding.etPhone.text.toString(),
                                    null
                                )
                            )
                        ) {
                            invokeListener()
                        }
                    } catch (_: Exception) {}
            }
        }
    }
}

