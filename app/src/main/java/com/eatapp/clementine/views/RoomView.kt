package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.View.MeasureSpec
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.get
import androidx.databinding.DataBindingUtil
import com.bumptech.glide.Glide
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.room.TableType
import com.eatapp.clementine.databinding.RoomViewBinding

// Extension function to set center position
fun View.setCenterPosition(centerX: Float, centerY: Float) {
    x = centerX - width / 2f
    y = centerY - height / 2f
}

class RoomView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val roomImageFormat: String = "https://ucarecdn.com/%s/-/preview/800x800/"

    var listener: ((tableState: TableState)->Unit)? = null

    private var binding: RoomViewBinding = DataBindingUtil.inflate(LayoutInflater.from(context),
        R.layout.room_view, this, true)

    fun loadFloor(states: List<TableState>, backgroundImageId: String?) {
        removeAllTables()

        states.forEach {
            addTable(it)
        }
        loadBackgroundImage(backgroundImageId)
    }

    fun updateFloor() {
        for (i in 0 until binding.cont.childCount) {
            val tableView: TableView? = binding.cont[i] as? TableView

            tableView?.tableState?.let {
                if (it.shouldUpdateView) {
                    binding.cont.removeView(tableView)
                    addTable(it, i)
                }
            }
        }
    }

    private fun removeAllTables() {
        binding.cont.removeAllViews()
    }

    private fun addTable(tableState: TableState, index: Int = 0) {

        val roomPadding = resources.getDimension(R.dimen.room_view_adjustment_padding)*2

        val roomWidth: Float = measuredWidth - roomPadding
        val roomHeight = measuredHeight - roomPadding

        val tableWidth = (roomWidth/75) * tableState.table.width
        val tableHeight = (roomWidth/75) * tableState.table.height

        val tableView = TableView(context!!)
        tableView.initWithTable(tableState, tableWidth.toInt(), tableHeight.toInt())

        tableView.layoutParams = ViewGroup.LayoutParams(
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        
        // Force measure and layout to get actual dimensions after rotation
        tableView.measure(
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        )
        tableView.layout(0, 0, tableView.measuredWidth, tableView.measuredHeight)

        val shape = tableView.binding.mainCont
        val shapeCenterX = shape.x + shape.width / 2
        val shapeCenterY = shape.y + shape.height / 2

        tableView.x = (roomWidth * tableState.table.x).toFloat() - shapeCenterX + roomPadding/2
        tableView.y = (roomHeight * tableState.table.y).toFloat() - shapeCenterY + roomPadding/2

        Log.d("tableview", "Table ${tableState.table.number}: x=${tableView.x}," +
                " y=${tableView.y}, w=${tableView.width}, h=${tableView.height}")

        tableView.listener = { state ->
            listener?.invoke(state)
        }

        tableView.z = if (tableState.table.type == TableType.Table) 99F else 88F

        binding.cont.addView(tableView, index)
    }

    private fun loadBackgroundImage(imageId: String?) {

        if (imageId?.isNotEmpty() == true) {
            val imageUrl = String.format(roomImageFormat, imageId)
            Glide.with(this)
                .load(imageUrl)
                .into(binding.backgroundImage)
        } else {
            binding.backgroundImage.setImageDrawable(null)
        }
    }
}