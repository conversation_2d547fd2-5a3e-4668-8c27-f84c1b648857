package com.eatapp.clementine.views

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.databinding.DataBindingUtil
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.room.TableType
import com.eatapp.clementine.databinding.TableViewBinding
import com.eatapp.clementine.internal.Status
import java.util.Date
import androidx.core.graphics.toColorInt
import com.eatapp.clementine.data.network.response.server.Server
import com.eatapp.clementine.internal.blendColors
import java.text.SimpleDateFormat
import java.util.Locale


class TableView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var listener: ((tableState: TableState) -> Unit)? = null

    lateinit var tableState: TableState

    var binding: TableViewBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        R.layout.table_view, this, true
    )

    fun initWithTable(tableState: TableState, tableWidth: Int, tableHeight: Int) {
        this.tableState = tableState

        binding.shape.layoutParams = ViewGroup.LayoutParams(
            tableWidth, tableHeight
        )

        if (isSingleEmoji(tableState.table.number) && tableState.table.type == TableType.Shape) {

            binding.emoji.text = tableState.table.number
            binding.tableShape.visibility = INVISIBLE
            binding.upcoming.visibility = GONE

        } else {

            // Check if iconColor is a resource ID by seeing if it's in the valid resource ID range
            val iconAndTextColor = if (tableState.iconTextColor and 0xFF000000.toInt() == 0x7F000000) {
                // It's a resource ID, get the actual color
                ContextCompat.getColor(context, tableState.iconTextColor)
            } else {
                // It's a direct color int
                tableState.iconTextColor
            }

            binding.title.text = tableState.table.number
            binding.title.setTextColor(
                blendColors(iconAndTextColor,
                    ContextCompat.getColor(binding.root.context, R.color.black), 0.4f)
            )

            tableState.icon?.let {
                binding.icon.setImageResource(it)

                binding.icon.setColorFilter(
                    blendColors(iconAndTextColor,
                        ContextCompat.getColor(binding.root.context, R.color.black), 0.4f)
                )
            } ?: run {
                binding.icon.visibility = GONE
            }

            if (tableState.table.width <= 4) {
                binding.icon.visibility = GONE
            }

            binding.shape.initWithTable(tableState)
            binding.shapeCont.angle = -tableState.table.rotation

            binding.emoji.visibility = GONE
        }

        when (tableState.hasUpcomingReservation) {
            true -> {
                binding.upcoming.visibility = VISIBLE

                binding.upcomingStatus.setImageResource(tableState.upcomingReservationIcon)
                binding.upcomingStatus.setColorFilter(
                    tableState.upcomingReservationColor,
                    PorterDuff.Mode.SRC_ATOP
                )
                binding.upcomingTitle.text = tableState.upcomingReservationTime
            }
            false -> binding.upcoming.visibility = GONE
        }

        requestLayout()

        if (tableState.table.type != TableType.Shape) {
            setOnClickListener {
                listener?.invoke(this.tableState)
            }
        }
    }

    private fun isSingleEmoji(input: String): Boolean {
        val regex = Regex("[\\p{So}\\p{Sk}]")
        return input.count() <= 2 && regex.containsMatchIn(input)
    }
}

class Shape @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var tableState: TableState? = null

    private val paint = Paint()

    fun initWithTable(tableState: TableState) {
        this.tableState = tableState
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (tableState == null) return

        if (tableState?.table?.shape == "circle") circlePath(canvas) else rectanglePath(canvas)
    }

    @SuppressLint("UseKtx")
    private fun circlePath(canvas: Canvas?) {
        val borderWidth = tableState?.strokeWidth?.let { resources.getDimension(it) }
            ?: resources.getDimension(R.dimen.tableview_border_width)

        // outer fill
        val outerFill = if (tableState?.table?.type == TableType.Shape)
            Color.parseColor(tableState?.table?.color) else
            ContextCompat.getColor(context, R.color.white)
        paint.style = Paint.Style.FILL
        paint.color = outerFill
        canvas?.drawOval(0f, 0f, width.toFloat(), height.toFloat(), paint)

        val innerPadding = resources.getDimension(R.dimen.tableview_inner_padding) + borderWidth/2

        // inner fill
        val color = if (tableState?.table?.type == TableType.Shape) {
            Color.parseColor(tableState?.table?.color)
        } else {
            if (tableState!!.fillColor > 0)
                ColorUtils.setAlphaComponent(ContextCompat.getColor(context, tableState!!.fillColor), (0.4f * 255).toInt()) else
                ColorUtils.setAlphaComponent(tableState!!.fillColor, (0.4f * 255).toInt())
        }
        paint.style = Paint.Style.FILL
        paint.color = color
        canvas?.drawOval(innerPadding, innerPadding, width.toFloat()-innerPadding, height.toFloat()-innerPadding, paint)

        // Check if strokeColor is a resource ID by seeing if it's in the valid resource ID range
        val strokeColor = if (tableState!!.strokeColor and 0xFF000000.toInt() == 0x7F000000) {
            // It's a resource ID, get the actual color
            ContextCompat.getColor(context, tableState!!.strokeColor)
        } else {
            // It's a direct color int
            tableState!!.strokeColor
        }

        // border
        paint.style = Paint.Style.STROKE
        paint.color = if (tableState?.table?.type == TableType.Shape)
            Color.parseColor(tableState?.table?.color) else strokeColor
        paint.strokeWidth = borderWidth
        canvas?.drawOval(borderWidth/2, borderWidth/2, width.toFloat()-borderWidth/2,
            height.toFloat()-borderWidth/2, paint)
    }

    private fun rectanglePath(canvas: Canvas?) {
        val borderWidth = tableState?.strokeWidth?.let { resources.getDimension(it) }
            ?: resources.getDimension(R.dimen.tableview_border_width)

        val radius = context.resources.getDimension(R.dimen.tableview_shape_radius)

        // outer fill
        val outerFill = if (tableState?.table?.type == TableType.Shape)
            tableState?.table?.color?.toColorInt() else
            ContextCompat.getColor(context, R.color.white)
        paint.style = Paint.Style.FILL
        paint.color = outerFill ?: R.color.grey800
        canvas?.drawRoundRect(0f, 0f, width.toFloat(),
            height.toFloat(), radius, radius , paint)

        val innerPadding = resources.getDimension(R.dimen.tableview_inner_padding) + borderWidth/2

        // inner fill
        val color = if (tableState?.table?.type == TableType.Shape) {
            tableState?.table?.color?.toColorInt()
        } else {
            if (tableState!!.fillColor > 0)
                ColorUtils.setAlphaComponent(ContextCompat.getColor(context, tableState!!.fillColor), (0.4f * 255).toInt()) else
                ColorUtils.setAlphaComponent(tableState!!.fillColor, (0.4f * 255).toInt())
        }
        paint.style = Paint.Style.FILL
        paint.color = color ?: R.color.grey800
        canvas?.drawRoundRect(innerPadding, innerPadding, width.toFloat()-innerPadding,
            height.toFloat()-innerPadding, 8f, 8f , paint)

        // Check if strokeColor is a resource ID by seeing if it's in the valid resource ID range
        val strokeColor = if (tableState!!.strokeColor and 0xFF000000.toInt() == 0x7F000000) {
            // It's a resource ID, get the actual color
            ContextCompat.getColor(context, tableState!!.strokeColor)
        } else {
            // It's a direct color int
            tableState!!.strokeColor
        }

        // border
        paint.style = Paint.Style.STROKE
        paint.color = if (tableState?.table?.type == TableType.Shape)
            tableState?.table?.color?.toColorInt() ?: R.color.grey800 else strokeColor
        paint.strokeWidth = borderWidth
        canvas?.drawRoundRect(borderWidth/2, borderWidth/2, width.toFloat()-borderWidth/2,
            height.toFloat()-borderWidth/2, radius, radius, paint)
    }
}

class TableState(
    var table: Table,
    var server: Server?,
    var reservations: Set<Reservation> = setOf(),
    var isFloor: Boolean = false,
    var isOccupied: Boolean = false,
    var isReserved: Boolean = false,
    var isSelected: Boolean = false
) {
    var fillColor: Int = 0
    var strokeColor: Int = 0
    var strokeWidth: Int = 0
    var icon: Int? = null
    var iconTextColor: Int = 0

    var shouldUpdateView: Boolean = false

    var hasUpcomingReservation: Boolean = false
    var upcomingReservationIcon: Int = 0
    var upcomingReservationTime: String = ""
    var upcomingReservationColor: Int = 0

    private val seatedReservations: Set<Reservation>
        get() = reservations.filter { Status.isSeated(it.status) }.toSet()

    private val upcomingReservations: Set<Reservation>
        get() = reservations.filter { Status.isUpcomingUi(it.status) }.toSet()

    init {
        this.updateConfig()
    }

    fun updateConfig() {
        icon = null

        var fillColor: Int = R.color.white
        var strokeColor: Int = server?.color?.toColorInt() ?: run { R.color.grey200 }
        var icon: Int? = null
        var iconTextColor: Int = R.color.grey800
        var strokeWidth: Int = if (server == null) R.dimen.tableview_border_width else R.dimen.tableview_border_width_server

        if (isSelected) {

            fillColor = R.color.green100
            strokeColor = R.color.green500
            icon = R.drawable.ic_icon_check
            iconTextColor = R.color.green500
            strokeWidth = R.dimen.tableview_border_width_bold

        } else if (isReserved || isOccupied) {

            fillColor = R.color.red300
            icon = if (isReserved) { R.drawable.ic_icon_lock } else { R.drawable.ic_icon_occupied }
            iconTextColor = R.color.red500

        } else if (seatedReservations.isNotEmpty() && isFloor) {

            val firstSeated = seatedReservations.minByOrNull { it.startTime }!!
            icon = Status.getTableIcon(firstSeated.status)
            iconTextColor = Status.getColor(firstSeated.status)
            fillColor = Status.getColor(firstSeated.status)

        } else if (upcomingReservations.isNotEmpty() && isFloor) {

            upcomingReservations.filter {
                it.startTime == Date() || it.endTime == Date() ||
                        (it.startTime.before(Date()) && it.endTime.after(Date()))
            }.minByOrNull { it.startTime }?.let {
                icon = Status.getTableIcon(it.status)
                iconTextColor = Status.getColor(it.status)
                fillColor = Status.getColor(it.status)
            }
        }

        // Handle upcoming reservation display
        upcomingReservations.minByOrNull { it.startTime }?.let {
            hasUpcomingReservation = true
            upcomingReservationIcon = Status.getIcon(it.status)
            upcomingReservationTime = SimpleDateFormat("hh:mm", Locale.US).format(it.startTime)
            upcomingReservationColor = Status.getColor(it.status)
        }

        this.fillColor = fillColor
        this.strokeColor = strokeColor
        this.icon = icon
        this.iconTextColor = iconTextColor
        this.strokeWidth = strokeWidth
    }
}