package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.widget.ViewPager2
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.internal.px
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

class EatTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TabLayout(context, attrs, defStyleAttr) {

    private var selectedTabWidth = 100.px // Default 100dp for selected tab
    private var adapter: EatPagerAdapter? = null
    private var viewPager: ViewPager2? = null
    private var mediator: TabLayoutMediator? = null

    init {
        // Set default attributes for the custom tab layout
        tabMode = MODE_FIXED
        tabGravity = GRAVITY_FILL
        setPadding(0, 0, 0, 0)
        
        // Add tab selection listener
        addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: Tab?) {
                updateAllTabsSelection()
                redistributeTabWidths()
            }
            
            override fun onTabUnselected(tab: Tab?) {
                updateAllTabsSelection()
            }
            
            override fun onTabReselected(tab: Tab?) {}
        })
    }

    /**
     * Set up the tab layout with ViewPager2 and adapter
     */
    fun setupWithViewPager(
        viewPager2: ViewPager2,
        pagerAdapter: EatPagerAdapter,
        selectedTabWidthDp: Int = 100
    ) {
        this.viewPager = viewPager2
        this.adapter = pagerAdapter
        this.selectedTabWidth = selectedTabWidthDp.px
        
        // Detach any existing mediator
        mediator?.detach()
        
        // Create new mediator
        mediator = TabLayoutMediator(this, viewPager2) { tab, position ->
            setupCustomTab(tab, position)
        }
        mediator?.attach()
        
        // Set initial selection state
        post {
            updateAllTabsSelection()
            redistributeTabWidths()
        }
    }

    /**
     * Add a fragment with title and icon to the adapter
     */
    fun addFragment(fragment: Fragment, title: String, iconRes: Int) {
        adapter?.addFragment(fragment, title, iconRes)
    }

    private fun setupCustomTab(tab: Tab, position: Int) {
        val customView = LayoutInflater.from(context).inflate(R.layout.custom_tab_layout, null)
        val tabIcon = customView.findViewById<ImageView>(R.id.tab_icon)
        val tabText = customView.findViewById<TextView>(R.id.tab_text)
        
        adapter?.let { adapter ->
            tabIcon.setImageResource(adapter.getPageIcon(position))
            tabText.text = adapter.getPageTitle(position)
        }
        
        tab.customView = customView
    }
    
    private fun updateAllTabsSelection() {
        val selectedPosition = selectedTabPosition
        
        for (i in 0 until tabCount) {
            val tab = getTabAt(i)
            val customView = tab?.customView
            val tabIcon = customView?.findViewById<ImageView>(R.id.tab_icon)
            val tabText = customView?.findViewById<TextView>(R.id.tab_text)
            
            if (i == selectedPosition) {
                // Selected tab: show icon + text
                tabText?.visibility = View.VISIBLE
                tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorPrimary))
                tabText?.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary))
            } else {
                // Unselected tab: show only icon
                tabText?.visibility = View.GONE
                tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorDark50))
            }
        }
    }
    
    private fun redistributeTabWidths() {
        val selectedPosition = selectedTabPosition
        val tabCount = tabCount
        
        if (tabCount == 0) return
        
        post {
            val totalWidth = width
            if (totalWidth <= 0) return@post
            
            // Calculate remaining width for unselected tabs
            val remainingWidth = totalWidth - selectedTabWidth
            val unselectedTabCount = tabCount - 1
            val unselectedTabWidth = if (unselectedTabCount > 0) {
                maxOf(remainingWidth / unselectedTabCount, 48.px) // Minimum 48dp per tab
            } else {
                totalWidth // If only one tab, it takes full width
            }
            
            // Apply the calculated widths
            for (i in 0 until tabCount) {
                val tab = getTabAt(i)
                val customView = tab?.customView
                
                if (customView != null) {
                    val layoutParams = customView.layoutParams
                    if (i == selectedPosition) {
                        layoutParams.width = selectedTabWidth
                    } else {
                        layoutParams.width = unselectedTabWidth
                    }
                    customView.layoutParams = layoutParams
                }
            }
        }
    }

    /**
     * Set the width for the selected tab in dp
     */
    fun setSelectedTabWidth(widthDp: Int) {
        this.selectedTabWidth = widthDp.px
        redistributeTabWidths()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mediator?.detach()
    }
}
