package com.eatapp.clementine.ui.reservation

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Point
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.core.graphics.scale
import androidx.viewpager2.widget.ViewPager2
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.adapter.ReservationHistoryAdapter
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.databinding.ReservationFragmentBinding
import com.eatapp.clementine.databinding.StatusHistoryLayoutBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.showAlert
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.messages.MessagesFragment
import com.eatapp.clementine.ui.common.pos.ReservationPosFragment
import com.eatapp.clementine.ui.common.printer.PrinterFragment
import com.eatapp.clementine.ui.reservation.payments.ReservationPaymentsFragment
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ReservationFragment : BaseFragment<ReservationViewModel, ReservationFragmentBinding>() {

    private lateinit var adapter: EatPagerAdapter

    override fun viewModelClass() = ReservationViewModel::class.java

    override fun inflateLayout() = ReservationFragmentBinding.inflate(layoutInflater)

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun viewCreated() {

        activity?.intent?.extras?.let {
            try {
                it.getParcelable<Reservation>(Constants.RESERVATION_EXTRA)?.let { r ->
                    vm.reservation(r)
                } ?: run {
                    it.getString(Constants.RESERVATION_KEY_EXTRA)?.let { reservationId ->
                        vm.loadReservation(reservationId)
                    } ?: run {
                        vm.reservation(null)
                    }
                }
            } catch (_: Exception) {
            }
        }

        observe()
    }

    private fun observe() {

        vm.reservation.observe(viewLifecycleOwner) {
            (activity as? ReservationActivity)?.reservation = it
            bindUI(it)
        }

        vm.fetchReservationError.observe(viewLifecycleOwner) {
            if (it) {
                requireContext().showAlert(
                    getString(R.string.reservation_error_title),
                    getString(R.string.reservation_error_desc),
                    positiveButtonText = resources.getString(R.string.go_back),
                    positiveButtonListener = {
                        requireActivity().finish()
                    })
            }
        }
    }

    private fun bindUI(reservation: Reservation?) {
        adapter = EatPagerAdapter(requireActivity())

        adapter.addFragment(
            ReservationDetailsFragment(),
            resources.getString(R.string.details),
            R.drawable.ic_icon_main // Using existing icon as temporary
        )

        if (reservation != null) {
            adapter.addFragment(
                MessagesFragment(),
                resources.getString(R.string.messages),
                R.drawable.message_alert // Using existing icon as temporary
            )
            adapter.addFragment(
                ReservationPosFragment(),
                resources.getString(R.string.pos),
                R.drawable.ic_icon_pos // Using existing icon as temporary
            )
        }

        if ((reservation != null) && vm.eatManager.restaurant()?.paymentsActivated == true) {
            adapter.addFragment(
                ReservationPaymentsFragment(),
                getString(R.string.payment),
                R.drawable.ic_payment // Using existing icon as temporary
            )
        }

        binding.viewPagerRF.adapter = adapter
        binding.viewPagerRF.offscreenPageLimit = ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT

        when (reservation) {
            null -> {
                binding.tabLayoutRF.visibility = View.GONE
                binding.separator.visibility = View.GONE
            }

            else -> {
                TabLayoutMediator(binding.tabLayoutRF, binding.viewPagerRF) { tab, position ->
                    setupCustomTab(tab, position)
                }.attach()

                // Add tab selection listener
                binding.tabLayoutRF.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                    override fun onTabSelected(tab: TabLayout.Tab?) {
                        tab?.position?.let { updateTabSelection(it) }
                    }

                    override fun onTabUnselected(tab: TabLayout.Tab?) {
                        // Update all tabs to ensure proper state
                        updateAllTabsSelection()
                    }

                    override fun onTabReselected(tab: TabLayout.Tab?) {}
                })

                // Set initial selection state after a short delay to ensure tabs are created
                binding.tabLayoutRF.post {
                    updateAllTabsSelection()
                    redistributeTabWidths()
                }
            }
        }

        (activity as? ReservationActivity)?.findViewById<ImageButton>(R.id.printBtn)?.visibility = if (reservation == null) View.INVISIBLE else View.VISIBLE
        (activity as? ReservationActivity)?.findViewById<ImageButton>(R.id.printBtn)?.setOnClickListener {
            printReservation()
        }

        (activity as? ReservationActivity)?.findViewById<ImageButton>(R.id.infoButton)?.visibility = if (reservation == null) View.INVISIBLE else View.VISIBLE
        (activity as? ReservationActivity)?.findViewById<ImageButton>(R.id.infoButton)?.setOnClickListener {
            showStatusHistory()
        }
    }

    private fun setupCustomTab(tab: TabLayout.Tab, position: Int) {
        val customView = LayoutInflater.from(requireContext()).inflate(R.layout.custom_tab_layout, null)
        val tabIcon = customView.findViewById<ImageView>(R.id.tab_icon)
        val tabText = customView.findViewById<TextView>(R.id.tab_text)

        tabIcon.setImageResource(adapter.getPageIcon(position))
        tabText.text = adapter.getPageTitle(position)

        tab.customView = customView
    }

    private fun updateTabSelection(selectedPosition: Int) {
        updateAllTabsSelection()
        redistributeTabWidths()
    }

    private fun updateAllTabsSelection() {
        val selectedPosition = binding.tabLayoutRF.selectedTabPosition

        for (i in 0 until binding.tabLayoutRF.tabCount) {
            val tab = binding.tabLayoutRF.getTabAt(i)
            val customView = tab?.customView
            val tabIcon = customView?.findViewById<ImageView>(R.id.tab_icon)
            val tabText = customView?.findViewById<TextView>(R.id.tab_text)

            if (i == selectedPosition) {
                // Selected tab: show icon + text
                tabText?.visibility = View.VISIBLE
                tabIcon?.setColorFilter(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
                tabText?.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
            } else {
                // Unselected tab: show only icon
                tabText?.visibility = View.GONE
                tabIcon?.setColorFilter(ContextCompat.getColor(requireContext(), R.color.colorDark50))
            }
        }
    }

    private fun redistributeTabWidths() {
        val tabLayout = binding.tabLayoutRF
        val selectedPosition = tabLayout.selectedTabPosition
        val tabCount = tabLayout.tabCount

        if (tabCount == 0) return

        tabLayout.post {
            val totalWidth = tabLayout.width
            if (totalWidth <= 0) return@post

            // Fixed width for selected tab (100dp)
            val selectedTabWidth = 120.px

            // Calculate remaining width for unselected tabs
            val remainingWidth = totalWidth - selectedTabWidth
            val unselectedTabCount = tabCount - 1
            val unselectedTabWidth = if (unselectedTabCount > 0) {
                maxOf(remainingWidth / unselectedTabCount, 48.px) // Minimum 48dp per tab
            } else {
                totalWidth // If only one tab, it takes full width
            }

            // Apply the calculated widths
            for (i in 0 until tabCount) {
                val tab = tabLayout.getTabAt(i)
                val customView = tab?.customView

                if (customView != null) {
                    val layoutParams = customView.layoutParams
                    if (i == selectedPosition) {
                        layoutParams.width = selectedTabWidth
                    } else {
                        layoutParams.width = unselectedTabWidth
                    }
                    customView.layoutParams = layoutParams
                }
            }
        }
    }

    private fun printReservation() {
        if (!vm.printManager.isConnected) {
            requireContext().showErrorAlert(
                resources.getString(R.string.no_printer_connected_title),
                resources.getString(R.string.no_printer_connected_desc)
            )
            return
        }

        if (vm.eatManager.showChitPrintConfig) {
            bottomSheetFragment(
                resources.getString(R.string.chit_print_config),
                PrinterFragment.newInstance(true), onDismiss = {
                    print()
                }
            )
        } else {
            print()
        }
    }

    private fun print() {
        val logo: Bitmap? =
            ContextCompat.getDrawable(requireContext(), R.drawable.ic_logo)?.toBitmap()
                ?.scale(120, 60)

        vm.printReservation(logo)
    }

    private fun showStatusHistory() {
        val popupView = StatusHistoryLayoutBinding.inflate(
            LayoutInflater.from(binding.root.context), null, false
        )

        showPopup(popupView.root, (activity as? ReservationActivity)?.findViewById(R.id.infoButton)!!)

        val adapter = ReservationHistoryAdapter()
        popupView.rvHistoryItems.adapter = adapter

        vm.reservation.value?.let {
            adapter.createList(it)
        }
    }

    private fun showPopup(viewToShow: View, anchorView: View): PopupWindow {

        val screenWidth = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            requireActivity().windowManager.currentWindowMetrics.bounds.width()
        } else {
            val display = requireActivity().windowManager.defaultDisplay
            val size = Point()
            display.getSize(size)
            size.x
        }

        // Create the PopupWindow
        val popupWindow = PopupWindow(
            viewToShow,
            screenWidth - 32.px,
            WRAP_CONTENT
        )

        popupWindow.elevation = 10f

        // Set focusable to true so the popup can be dismissed by touching outside
        popupWindow.isFocusable = true

        // Position the popup window next to the clicked item
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)

        val y = location[1] + anchorView.height

        // Show the popup window with the custom view
        popupWindow.showAtLocation(anchorView, Gravity.NO_GRAVITY, 16.px, y)

        // Dim the background
        val container = popupWindow.contentView.parent as? View
        val windowManager = anchorView.context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val layoutParams = container?.layoutParams as? WindowManager.LayoutParams
        layoutParams?.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND
        layoutParams?.dimAmount = 0.2f
        windowManager.updateViewLayout(container, layoutParams)

        return popupWindow
    }
}

