package com.eatapp.clementine

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.LogLevel
import com.datadog.android.Datadog
import com.datadog.android.DatadogSite
import com.datadog.android.core.configuration.Configuration
import com.datadog.android.core.configuration.Credentials
import com.datadog.android.privacy.TrackingConsent
import com.datadog.android.rum.tracking.MixedViewTrackingStrategy
import com.eatapp.clementine.internal.Status
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class EatApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        Status.resources = this.resources
        Status.packageName = this.packageName
        /*
         * Adjust
        */
        val environment = if (BuildConfig.DEBUG || BuildConfig.FLAVOR == "stage")
            AdjustConfig.ENVIRONMENT_SANDBOX
        else
            AdjustConfig.ENVIRONMENT_PRODUCTION

        val config = AdjustConfig(this, BuildConfig.ADJUST_TOKEN, environment)
        config.setLogLevel(LogLevel.VERBOSE)
        Adjust.onCreate(config)

        registerActivityLifecycleCallbacks(AdjustLifecycleCallbacks())

        /*
         * Datadog
        */

        val configuration = Configuration.Builder(
            rumEnabled = false,
            crashReportsEnabled = true,
            logsEnabled = true,
            tracesEnabled = true
        )
            .trackInteractions()
            .useViewTrackingStrategy(MixedViewTrackingStrategy(trackExtras = true))
            .useSite(DatadogSite.EU1)
            .build()

        val credentials = Credentials(
            clientToken = BuildConfig.DATADOG_TOKEN,
            envName = BuildConfig.ENVIRONMENT,
            serviceName = BuildConfig.USER_AGENT,
            rumApplicationId = BuildConfig.DATADOG_APP_ID,
            variant = ""
        )
        Datadog.initialize(this, credentials, configuration, TrackingConsent.GRANTED)

        Datadog.setVerbosity(android.util.Log.DEBUG)
    }

    data class ApiArguments(val baseUrl: String)
    data class BaseUrls(val websocket: ApiArguments, val eatApiService: ApiArguments)

    class AdjustLifecycleCallbacks : ActivityLifecycleCallbacks {

        override fun onActivityResumed(activity: Activity) {
            Adjust.onResume()
        }

        override fun onActivityPaused(activity: Activity) {
            Adjust.onPause()
        }

        override fun onActivityStarted(p0: Activity) {}

        override fun onActivityDestroyed(p0: Activity) {}

        override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {}

        override fun onActivityStopped(p0: Activity) {}

        override fun onActivityCreated(p0: Activity, p1: Bundle?) {}
    }
}