package com.eatapp.clementine.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentStatus
import com.eatapp.clementine.databinding.ListItemReservationPaymentBinding
import com.eatapp.clementine.internal.copyToClipboard
import com.eatapp.clementine.internal.shortDateTime
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.reservation.payments.PaymentItemClickListener
import com.eatapp.clementine.ui.reservation.payments.PaymentLinkClickListener

class ReservationPaymentListItem @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding: ListItemReservationPaymentBinding

    init {
        binding =
            ListItemReservationPaymentBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun update(
        payment: Payment,
        paymentLinkClickListener: PaymentLinkClickListener? = null,
        itemClickListener: PaymentItemClickListener? = null,
        paymentLinkVisible: Boolean = true,
        chevronVisible: Boolean = true
    ) = with(binding) {
        // Bank code container
        containerBankCode.visibleOrGone = false

        // Created at
        payment.attributes.createdAt?.let {
            textCreated.text = it.shortDateTime()
        }

        // Updated at
        textUpdated.text = if (payment.attributes.updatedAt == null) {
            "--"
        } else {
            payment.attributes.updatedAt.shortDateTime()
        }

        // Expires at
        containerExpires.visible = payment.attributes.autoCancelAt != null
        payment.attributes.autoCancelAt?.let {
            textExpires.text = it.shortDateTime()
        }

        // Amount
        amountValue.text =
            String.format("%.2f %s", payment.attributes.amount, payment.attributes.currency)

        // Status
        statusValue.text = PaymentStatus.valueOf(payment.status.uppercase()).readableStatus()
        imageStatus.setImageResource(PaymentStatus.icon(payment.status))

        // Payment link
        containerPaymentLink.visibleOrGone = paymentLinkVisible && payment.attributes.paymentWidgetUrl != null
        textPaymentLink.text = payment.attributes.paymentWidgetUrl
        textPaymentLink.setOnClickListener {
            paymentLinkClickListener?.invoke(textPaymentLink.text.toString())
        }

        imageChevron.visibleOrGone = chevronVisible

        imageCopy.setOnClickListener {
            context.copyToClipboard("Payment link", textPaymentLink.text.toString())
        }

        setOnClickListener {
            itemClickListener?.invoke(payment)
        }
    }
}